package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 📏 线段测量助手 - 专业级测量管理系统
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000v\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010!\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\f\u0018\u0000 <2\u00020\u0001:\u0001<B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u001b\u001a\u00020\u001cJ\u0006\u0010\u001d\u001a\u00020\u0015J\u0006\u0010\u001e\u001a\u00020\u0015J\u0006\u0010\u001f\u001a\u00020\u0015J\u0010\u0010 \u001a\u00020\u00152\u0006\u0010!\u001a\u00020\u0004H\u0002J\u0006\u0010\"\u001a\u00020\nJ\f\u0010#\u001a\b\u0012\u0004\u0012\u00020%0$J\u0006\u0010&\u001a\u00020\u0006J\u001e\u0010\'\u001a\u00020\n2\u0006\u0010(\u001a\u00020)2\u0006\u0010*\u001a\u00020\u00062\u0006\u0010+\u001a\u00020\u0006J\u0006\u0010,\u001a\u00020\nJ\u0016\u0010-\u001a\u00020\u00152\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010.\u001a\u00020\u0019J\u0006\u0010\t\u001a\u00020\nJ \u0010/\u001a\u00020\n2\u0006\u00100\u001a\u0002012\u0006\u0010*\u001a\u00020\u00062\u0006\u0010+\u001a\u00020\u0006H\u0002J\u000e\u00102\u001a\u00020\n2\u0006\u00100\u001a\u000201J\u000e\u00103\u001a\u00020\n2\u0006\u00100\u001a\u000201J\u000e\u00104\u001a\u00020\n2\u0006\u00100\u001a\u000201J\b\u00105\u001a\u00020\u0015H\u0002J\u0006\u00106\u001a\u00020\u0015J\u0006\u00107\u001a\u00020\u0015J\b\u00108\u001a\u00020\u0015H\u0002J\u0014\u00109\u001a\u00020\u00152\f\u0010:\u001a\b\u0012\u0004\u0012\u00020\u00150\u0014J\u0006\u0010;\u001a\u00020\u001cR\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0013\u001a\n\u0012\u0004\u0012\u00020\u0015\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00040\u0017X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0019X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001a\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006="}, d2 = {"Lcom/touptek/measurerealize/utils/LineMeasureHelper;", "", "()V", "activeMeasurement", "Lcom/touptek/measurerealize/utils/LineMeasurement;", "draggedPointIndex", "", "imageView", "Landroid/widget/ImageView;", "isDraggingPoint", "", "lastTouchTime", "", "lastTouchX", "", "lastTouchY", "longPressRunnable", "Ljava/lang/Runnable;", "longPressStartTime", "measurementUpdateCallback", "Lkotlin/Function0;", "", "measurements", "", "originalBitmap", "Landroid/graphics/Bitmap;", "selectedMeasurement", "addNewMeasurement", "", "clearAllMeasurements", "clearAllSelections", "clearSelection", "deleteMeasurement", "measurement", "deleteSelectedMeasurement", "getAllMeasurementData", "", "Lcom/touptek/measurerealize/utils/LineMeasurementData;", "getMeasurementCount", "handleTouchEvent", "event", "Landroid/view/MotionEvent;", "viewWidth", "viewHeight", "hasSelectedMeasurement", "init", "bitmap", "isInImageContentArea", "touchPoint", "Landroid/graphics/PointF;", "isNearAnyMeasurement", "isPointOnMeasurement", "isTouchingMeasurementPoint", "notifyUpdate", "onScaleChanged", "reset", "resetInteractionState", "setMeasurementUpdateCallback", "callback", "startNewMeasurement", "Companion", "app_debug"})
public final class LineMeasureHelper {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.measurerealize.utils.LineMeasureHelper.Companion Companion = null;
    private static final java.lang.String TAG = "LineMeasureHelper";
    private static final float TOUCH_RADIUS = 80.0F;
    private static final float CLICK_TOLERANCE = 20.0F;
    private static final long LONG_PRESS_DURATION = 800L;
    private static final float DEFAULT_LINE_LENGTH = 100.0F;
    private android.widget.ImageView imageView;
    private android.graphics.Bitmap originalBitmap;
    private final java.util.List<com.touptek.measurerealize.utils.LineMeasurement> measurements = null;
    private com.touptek.measurerealize.utils.LineMeasurement selectedMeasurement;
    private com.touptek.measurerealize.utils.LineMeasurement activeMeasurement;
    private boolean isDraggingPoint = false;
    private int draggedPointIndex = -1;
    private long lastTouchTime = 0L;
    private java.lang.Runnable longPressRunnable;
    private long longPressStartTime = 0L;
    private float lastTouchX = 0.0F;
    private float lastTouchY = 0.0F;
    private kotlin.jvm.functions.Function0<kotlin.Unit> measurementUpdateCallback;
    
    public LineMeasureHelper() {
        super();
    }
    
    /**
     * 🚀 初始化助手
     */
    public final void init(@org.jetbrains.annotations.NotNull
    android.widget.ImageView imageView, @org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
    }
    
    /**
     * 🔄 设置测量更新回调
     */
    public final void setMeasurementUpdateCallback(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    /**
     * 🔔 通知更新
     */
    private final void notifyUpdate() {
    }
    
    /**
     * 🎯 开始新的线段测量 - 在屏幕中心生成默认线段
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String startNewMeasurement() {
        return null;
    }
    
    /**
     * 🎯 处理触摸事件 - 与AngleMeasureHelper保持一致的交互逻辑
     */
    public final boolean handleTouchEvent(@org.jetbrains.annotations.NotNull
    android.view.MotionEvent event, int viewWidth, int viewHeight) {
        return false;
    }
    
    /**
     * 🗑️ 删除测量
     */
    private final void deleteMeasurement(com.touptek.measurerealize.utils.LineMeasurement measurement) {
    }
    
    /**
     * 🔄 重置交互状态
     */
    private final void resetInteractionState() {
    }
    
    /**
     * 🎨 获取所有测量数据用于覆盖层显示
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.touptek.measurerealize.utils.LineMeasurementData> getAllMeasurementData() {
        return null;
    }
    
    /**
     * 🎯 获取测量数量
     */
    public final int getMeasurementCount() {
        return 0;
    }
    
    /**
     * 🎯 检查是否有选中的测量
     */
    public final boolean hasSelectedMeasurement() {
        return false;
    }
    
    /**
     * 🗑️ 删除选中的测量
     */
    public final boolean deleteSelectedMeasurement() {
        return false;
    }
    
    /**
     * 🎯 添加新的线段测量 - 与AngleMeasureHelper保持一致
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String addNewMeasurement() {
        return null;
    }
    
    /**
     * 🔄 处理缩放变化 - 同步所有测量的坐标
     */
    public final void onScaleChanged() {
    }
    
    /**
     * 🧹 清除所有选中状态
     */
    public final void clearAllSelections() {
    }
    
    /**
     * 🧹 清除所有测量
     */
    public final void clearAllMeasurements() {
    }
    
    /**
     * 🧹 重置助手
     */
    public final void reset() {
    }
    
    /**
     * 🎯 检查触摸点是否在任何测量上（用于智能模式激活）
     */
    public final boolean isPointOnMeasurement(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🎯 检查是否正在拖拽点
     */
    public final boolean isDraggingPoint() {
        return false;
    }
    
    /**
     * 🎯 检查是否触摸到测量点
     */
    public final boolean isTouchingMeasurementPoint(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🎯 检查是否靠近任何测量
     */
    public final boolean isNearAnyMeasurement(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🛡️ 检测触摸点是否在图像内容区域（避免UI按钮区域） - 与PointMeasureHelper保持一致
     */
    private final boolean isInImageContentArea(android.graphics.PointF touchPoint, int viewWidth, int viewHeight) {
        return false;
    }
    
    /**
     * 🎯 清除所有选择状态
     */
    public final void clearSelection() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/touptek/measurerealize/utils/LineMeasureHelper$Companion;", "", "()V", "CLICK_TOLERANCE", "", "DEFAULT_LINE_LENGTH", "LONG_PRESS_DURATION", "", "TAG", "", "TOUCH_RADIUS", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}