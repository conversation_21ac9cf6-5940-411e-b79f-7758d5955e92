<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_storage_settings" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\fragment_storage_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_storage_settings_0" view="ScrollView"><Expressions/><location startLine="0" startOffset="4" endLine="369" endOffset="12"/></Target><Target id="@+id/storage_location_group" view="RadioGroup"><Expressions/><location startLine="27" startOffset="12" endLine="45" endOffset="24"/></Target><Target id="@+id/radio_external" view="RadioButton"><Expressions/><location startLine="33" startOffset="16" endLine="38" endOffset="54"/></Target><Target id="@+id/radio_internal" view="RadioButton"><Expressions/><location startLine="40" startOffset="16" endLine="44" endOffset="44"/></Target><Target id="@+id/cb_smb_enable" view="CheckBox"><Expressions/><location startLine="65" startOffset="12" endLine="70" endOffset="51"/></Target><Target id="@+id/et_server_ip" view="EditText"><Expressions/><location startLine="87" startOffset="16" endLine="94" endOffset="45"/></Target><Target id="@+id/et_share_name" view="EditText"><Expressions/><location startLine="112" startOffset="16" endLine="119" endOffset="45"/></Target><Target id="@+id/et_username" view="EditText"><Expressions/><location startLine="137" startOffset="16" endLine="144" endOffset="45"/></Target><Target id="@+id/et_password" view="EditText"><Expressions/><location startLine="162" startOffset="16" endLine="169" endOffset="45"/></Target><Target id="@+id/btn_test_connection" view="Button"><Expressions/><location startLine="171" startOffset="16" endLine="178" endOffset="53"/></Target><Target id="@+id/sp_remote_path" view="Spinner"><Expressions/><location startLine="196" startOffset="16" endLine="200" endOffset="48"/></Target><Target id="@+id/btn_browse" view="Button"><Expressions/><location startLine="202" startOffset="16" endLine="209" endOffset="53"/></Target><Target id="@+id/tv_upload_status" view="TextView"><Expressions/><location startLine="227" startOffset="16" endLine="233" endOffset="44"/></Target><Target id="@+id/tv_last_upload" view="TextView"><Expressions/><location startLine="250" startOffset="16" endLine="256" endOffset="44"/></Target><Target id="@+id/cb_video_time_suffix" view="CheckBox"><Expressions/><location startLine="294" startOffset="16" endLine="299" endOffset="54"/></Target><Target id="@+id/et_video_prefix" view="EditText"><Expressions/><location startLine="313" startOffset="20" endLine="319" endOffset="46"/></Target><Target id="@+id/cb_image_time_suffix" view="CheckBox"><Expressions/><location startLine="339" startOffset="16" endLine="344" endOffset="54"/></Target><Target id="@+id/et_image_prefix" view="EditText"><Expressions/><location startLine="358" startOffset="20" endLine="364" endOffset="46"/></Target></Targets></Layout>