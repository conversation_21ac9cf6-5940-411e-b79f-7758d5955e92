package com.touptek.xcamview.activity.settings;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\b\n\u0002\b\u0012\u0018\u0000 >2\u00020\u0001:\u0001>B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u0017H\u0002J\b\u0010\u0018\u001a\u00020\u0015H\u0002J\b\u0010\u0019\u001a\u00020\u0015H\u0002J\b\u0010\u001a\u001a\u00020\u0015H\u0002J&\u0010\u001b\u001a\u0004\u0018\u00010\u001c2\u0006\u0010\u001d\u001a\u00020\u001e2\b\u0010\u001f\u001a\u0004\u0018\u00010 2\b\u0010!\u001a\u0004\u0018\u00010\"H\u0016J\u001a\u0010#\u001a\u00020\u00152\u0006\u0010$\u001a\u00020\u001c2\b\u0010!\u001a\u0004\u0018\u00010\"H\u0016J\b\u0010%\u001a\u00020\u0015H\u0002J\b\u0010&\u001a\u00020\u0015H\u0002J\u0010\u0010\'\u001a\u00020\u00152\u0006\u0010(\u001a\u00020\u0017H\u0002J\u0010\u0010)\u001a\u00020\u00152\u0006\u0010*\u001a\u00020\u0017H\u0002J\u0010\u0010+\u001a\u00020\u00152\u0006\u0010,\u001a\u00020-H\u0002J\u0010\u0010.\u001a\u00020\u00152\u0006\u0010/\u001a\u00020-H\u0002J\u0010\u00100\u001a\u00020\u00152\u0006\u0010/\u001a\u00020-H\u0002J\u0010\u00101\u001a\u00020\u00152\u0006\u00102\u001a\u00020\u0017H\u0002J\b\u00103\u001a\u00020\u0015H\u0002J\b\u00104\u001a\u00020\u0015H\u0002J\u0010\u00105\u001a\u00020\u00152\u0006\u00106\u001a\u00020-H\u0002J\u0010\u00107\u001a\u00020\u00152\u0006\u00108\u001a\u00020\u0017H\u0002J\b\u00109\u001a\u00020\u0015H\u0002J\b\u0010:\u001a\u00020\u0015H\u0002J\b\u0010;\u001a\u00020\u0015H\u0002J\b\u0010<\u001a\u00020\u0015H\u0002J\b\u0010=\u001a\u00020\u0015H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0007X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0007X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0011X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006?"}, d2 = {"Lcom/touptek/xcamview/activity/settings/TpFormatSettingsFragment;", "Landroidx/fragment/app/Fragment;", "()V", "colorDepthGroup", "Landroid/widget/RadioGroup;", "colorSpaceGroup", "compressionSpinner", "Landroid/widget/Spinner;", "fontSizeSpinner", "fusionCheck", "Landroid/widget/CheckBox;", "labelStyleSpinner", "layerCheck", "measurementMarksCheck", "measurementReportCheck", "measurementValuesCheck", "resetSettingsButton", "Landroid/widget/Button;", "saveModeGroup", "stylePreviewButton", "handleFormatSelected", "", "format", "", "loadAdvancedSettings", "loadMeasurementSettings", "loadSavedSettings", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onViewCreated", "view", "resetMeasurementSettings", "runCompatibilityTest", "saveColorDepth", "depth", "saveColorSpace", "space", "saveCompressionLevel", "level", "", "saveFontSize", "position", "saveLabelStyle", "saveMeasurementMode", "mode", "saveMeasurementSettings", "savePostProcessSettings", "saveQualitySetting", "quality", "saveResolution", "resolution", "setupAdvancedSettings", "setupFormatSelection", "setupMeasurementSettings", "showAdvancedParamDetails", "updateQualityControlState", "Companion", "app_debug"})
public final class TpFormatSettingsFragment extends androidx.fragment.app.Fragment {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.xcamview.activity.settings.TpFormatSettingsFragment.Companion Companion = null;
    private static final java.lang.String TAG = "FormatSettings";
    private android.widget.RadioGroup saveModeGroup;
    private android.widget.CheckBox measurementMarksCheck;
    private android.widget.CheckBox measurementValuesCheck;
    private android.widget.CheckBox measurementReportCheck;
    private android.widget.Spinner labelStyleSpinner;
    private android.widget.Spinner fontSizeSpinner;
    private android.widget.Button stylePreviewButton;
    private android.widget.Button resetSettingsButton;
    private android.widget.RadioGroup colorSpaceGroup;
    private android.widget.RadioGroup colorDepthGroup;
    private android.widget.Spinner compressionSpinner;
    private android.widget.CheckBox fusionCheck;
    private android.widget.CheckBox layerCheck;
    
    public TpFormatSettingsFragment() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable
    @java.lang.Override
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override
    public void onViewCreated(@org.jetbrains.annotations.NotNull
    android.view.View view, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupFormatSelection() {
    }
    
    private final void updateQualityControlState() {
    }
    
    private final void loadSavedSettings() {
    }
    
    private final void saveResolution(java.lang.String resolution) {
    }
    
    private final void handleFormatSelected(java.lang.String format) {
    }
    
    private final void saveQualitySetting(int quality) {
    }
    
    private final void setupMeasurementSettings() {
    }
    
    private final void loadMeasurementSettings() {
    }
    
    private final void saveMeasurementMode(java.lang.String mode) {
    }
    
    private final void saveMeasurementSettings() {
    }
    
    private final void saveLabelStyle(int position) {
    }
    
    private final void saveFontSize(int position) {
    }
    
    private final void resetMeasurementSettings() {
    }
    
    private final void setupAdvancedSettings() {
    }
    
    private final void loadAdvancedSettings() {
    }
    
    private final void saveColorSpace(java.lang.String space) {
    }
    
    private final void saveColorDepth(java.lang.String depth) {
    }
    
    private final void saveCompressionLevel(int level) {
    }
    
    private final void savePostProcessSettings() {
    }
    
    private final void showAdvancedParamDetails() {
    }
    
    private final void runCompatibilityTest() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/touptek/xcamview/activity/settings/TpFormatSettingsFragment$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}