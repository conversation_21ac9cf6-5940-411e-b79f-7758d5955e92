<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_format_settings" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\fragment_format_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_format_settings_0" view="ScrollView"><Expressions/><location startLine="0" startOffset="0" endLine="322" endOffset="12"/></Target><Target id="@+id/format_group" view="RadioGroup"><Expressions/><location startLine="29" startOffset="12" endLine="58" endOffset="24"/></Target><Target id="@+id/radio_jpeg" view="RadioButton"><Expressions/><location startLine="35" startOffset="16" endLine="39" endOffset="40"/></Target><Target id="@+id/radio_bmp" view="RadioButton"><Expressions/><location startLine="41" startOffset="16" endLine="45" endOffset="39"/></Target><Target id="@+id/radio_png" view="RadioButton"><Expressions/><location startLine="47" startOffset="16" endLine="51" endOffset="39"/></Target><Target id="@+id/radio_tiff" view="RadioButton"><Expressions/><location startLine="53" startOffset="16" endLine="57" endOffset="40"/></Target><Target id="@+id/save_mode_group" view="RadioGroup"><Expressions/><location startLine="84" startOffset="12" endLine="101" endOffset="24"/></Target><Target id="@+id/radio_fusion_mode" view="RadioButton"><Expressions/><location startLine="90" startOffset="16" endLine="94" endOffset="40"/></Target><Target id="@+id/radio_layer_mode" view="RadioButton"><Expressions/><location startLine="96" startOffset="16" endLine="100" endOffset="40"/></Target><Target id="@+id/check_save_measurement_marks" view="CheckBox"><Expressions/><location startLine="117" startOffset="16" endLine="121" endOffset="42"/></Target><Target id="@+id/check_save_measurement_values" view="CheckBox"><Expressions/><location startLine="123" startOffset="16" endLine="127" endOffset="42"/></Target><Target id="@+id/check_save_measurement_report" view="CheckBox"><Expressions/><location startLine="129" startOffset="16" endLine="133" endOffset="42"/></Target><Target id="@+id/spinner_label_style" view="Spinner"><Expressions/><location startLine="144" startOffset="12" endLine="150" endOffset="54"/></Target><Target id="@+id/spinner_font_size" view="Spinner"><Expressions/><location startLine="160" startOffset="12" endLine="166" endOffset="52"/></Target><Target id="@+id/btn_style_preview" view="Button"><Expressions/><location startLine="177" startOffset="16" endLine="183" endOffset="58"/></Target><Target id="@+id/btn_reset_settings" view="Button"><Expressions/><location startLine="185" startOffset="16" endLine="191" endOffset="58"/></Target><Target id="@+id/color_space_group" view="RadioGroup"><Expressions/><location startLine="218" startOffset="12" endLine="235" endOffset="24"/></Target><Target id="@+id/radio_srgb" view="RadioButton"><Expressions/><location startLine="223" startOffset="16" endLine="228" endOffset="40"/></Target><Target id="@+id/radio_adobe_rgb" view="RadioButton"><Expressions/><location startLine="229" startOffset="16" endLine="234" endOffset="45"/></Target><Target id="@+id/color_depth_group" view="RadioGroup"><Expressions/><location startLine="244" startOffset="12" endLine="261" endOffset="24"/></Target><Target id="@+id/radio_8bit" view="RadioButton"><Expressions/><location startLine="249" startOffset="16" endLine="254" endOffset="38"/></Target><Target id="@+id/radio_16bit" view="RadioButton"><Expressions/><location startLine="255" startOffset="16" endLine="260" endOffset="39"/></Target><Target id="@+id/spinner_compression" view="Spinner"><Expressions/><location startLine="270" startOffset="12" endLine="277" endOffset="51"/></Target><Target id="@+id/check_fusion_mode" view="CheckBox"><Expressions/><location startLine="286" startOffset="12" endLine="290" endOffset="38"/></Target><Target id="@+id/check_layer_mode" view="CheckBox"><Expressions/><location startLine="291" startOffset="12" endLine="295" endOffset="38"/></Target><Target id="@+id/btn_param_details" view="Button"><Expressions/><location startLine="304" startOffset="16" endLine="310" endOffset="58"/></Target><Target id="@+id/btn_compatibility_test" view="Button"><Expressions/><location startLine="311" startOffset="16" endLine="317" endOffset="58"/></Target></Targets></Layout>