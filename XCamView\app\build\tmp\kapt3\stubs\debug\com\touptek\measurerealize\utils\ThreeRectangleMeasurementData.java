package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 🎯 三点矩形测量数据 - 支持旋转的矩形测量
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b%\n\u0002\u0010\u0000\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001B\u009b\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0007\u0012\u0006\u0010\t\u001a\u00020\u0007\u0012\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00050\u000b\u0012\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00050\u000b\u0012\b\b\u0002\u0010\r\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u000e\u0012\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0013\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0013\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u0017\u00a2\u0006\u0002\u0010\u0018J\t\u0010+\u001a\u00020\u0003H\u00c6\u0003J\t\u0010,\u001a\u00020\u000eH\u00c6\u0003J\u000b\u0010-\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010.\u001a\u00020\u0013H\u00c6\u0003J\t\u0010/\u001a\u00020\u0013H\u00c6\u0003J\t\u00100\u001a\u00020\u000eH\u00c6\u0003J\t\u00101\u001a\u00020\u0017H\u00c6\u0003J\t\u00102\u001a\u00020\u0005H\u00c6\u0003J\t\u00103\u001a\u00020\u0007H\u00c6\u0003J\t\u00104\u001a\u00020\u0007H\u00c6\u0003J\t\u00105\u001a\u00020\u0007H\u00c6\u0003J\u000f\u00106\u001a\b\u0012\u0004\u0012\u00020\u00050\u000bH\u00c6\u0003J\u000f\u00107\u001a\b\u0012\u0004\u0012\u00020\u00050\u000bH\u00c6\u0003J\t\u00108\u001a\u00020\u000eH\u00c6\u0003J\t\u00109\u001a\u00020\u000eH\u00c6\u0003J\u00ad\u0001\u0010:\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\b\b\u0002\u0010\t\u001a\u00020\u00072\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00050\u000b2\u000e\b\u0002\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00050\u000b2\b\b\u0002\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u000e2\b\b\u0002\u0010\u0010\u001a\u00020\u000e2\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u0012\u001a\u00020\u00132\b\b\u0002\u0010\u0014\u001a\u00020\u00132\b\b\u0002\u0010\u0015\u001a\u00020\u000e2\b\b\u0002\u0010\u0016\u001a\u00020\u0017H\u00c6\u0001J\u0013\u0010;\u001a\u00020\u000e2\b\u0010<\u001a\u0004\u0018\u00010=H\u00d6\u0003J\t\u0010>\u001a\u00020\u0017H\u00d6\u0001J\t\u0010?\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0012\u001a\u00020\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00050\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001eR\u0011\u0010\u0016\u001a\u00020\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010 R\u0011\u0010\b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\"R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010$R\u0011\u0010\u0010\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010%R\u0011\u0010\u0015\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010%R\u0011\u0010\u000f\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010%R\u0011\u0010\r\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010%R\u0011\u0010\u0014\u001a\u00020\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\u001aR\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00050\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u001eR\u0011\u0010\t\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010\"R\u0013\u0010\u0011\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010\u001cR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010\"\u00a8\u0006@"}, d2 = {"Lcom/touptek/measurerealize/utils/ThreeRectangleMeasurementData;", "Lcom/touptek/measurerealize/utils/MeasurementData;", "id", "", "centerPoint", "Landroid/graphics/PointF;", "width", "", "height", "rotationAngle", "controlPoints", "", "rotatedCorners", "isSelected", "", "isEditing", "isCompleted", "textPosition", "area", "", "perimeter", "isDragging", "draggedPointIndex", "", "(Ljava/lang/String;Landroid/graphics/PointF;FFFLjava/util/List;Ljava/util/List;ZZZLandroid/graphics/PointF;DDZI)V", "getArea", "()D", "getCenterPoint", "()Landroid/graphics/PointF;", "getControlPoints", "()Ljava/util/List;", "getDraggedPointIndex", "()I", "getHeight", "()F", "getId", "()Ljava/lang/String;", "()Z", "getPerimeter", "getRotatedCorners", "getRotationAngle", "getTextPosition", "getWidth", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "", "hashCode", "toString", "app_debug"})
public final class ThreeRectangleMeasurementData extends com.touptek.measurerealize.utils.MeasurementData {
    @org.jetbrains.annotations.NotNull
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull
    private final android.graphics.PointF centerPoint = null;
    private final float width = 0.0F;
    private final float height = 0.0F;
    private final float rotationAngle = 0.0F;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<android.graphics.PointF> controlPoints = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<android.graphics.PointF> rotatedCorners = null;
    private final boolean isSelected = false;
    private final boolean isEditing = false;
    private final boolean isCompleted = false;
    @org.jetbrains.annotations.Nullable
    private final android.graphics.PointF textPosition = null;
    private final double area = 0.0;
    private final double perimeter = 0.0;
    private final boolean isDragging = false;
    private final int draggedPointIndex = 0;
    
    /**
     * 🎯 三点矩形测量数据 - 支持旋转的矩形测量
     */
    @org.jetbrains.annotations.NotNull
    public final com.touptek.measurerealize.utils.ThreeRectangleMeasurementData copy(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    android.graphics.PointF centerPoint, float width, float height, float rotationAngle, @org.jetbrains.annotations.NotNull
    java.util.List<? extends android.graphics.PointF> controlPoints, @org.jetbrains.annotations.NotNull
    java.util.List<? extends android.graphics.PointF> rotatedCorners, boolean isSelected, boolean isEditing, boolean isCompleted, @org.jetbrains.annotations.Nullable
    android.graphics.PointF textPosition, double area, double perimeter, boolean isDragging, int draggedPointIndex) {
        return null;
    }
    
    /**
     * 🎯 三点矩形测量数据 - 支持旋转的矩形测量
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 🎯 三点矩形测量数据 - 支持旋转的矩形测量
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 🎯 三点矩形测量数据 - 支持旋转的矩形测量
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public ThreeRectangleMeasurementData(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    android.graphics.PointF centerPoint, float width, float height, float rotationAngle, @org.jetbrains.annotations.NotNull
    java.util.List<? extends android.graphics.PointF> controlPoints, @org.jetbrains.annotations.NotNull
    java.util.List<? extends android.graphics.PointF> rotatedCorners, boolean isSelected, boolean isEditing, boolean isCompleted, @org.jetbrains.annotations.Nullable
    android.graphics.PointF textPosition, double area, double perimeter, boolean isDragging, int draggedPointIndex) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final android.graphics.PointF component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final android.graphics.PointF getCenterPoint() {
        return null;
    }
    
    public final float component3() {
        return 0.0F;
    }
    
    public final float getWidth() {
        return 0.0F;
    }
    
    public final float component4() {
        return 0.0F;
    }
    
    public final float getHeight() {
        return 0.0F;
    }
    
    public final float component5() {
        return 0.0F;
    }
    
    public final float getRotationAngle() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> getControlPoints() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> getRotatedCorners() {
        return null;
    }
    
    public final boolean component8() {
        return false;
    }
    
    public final boolean isSelected() {
        return false;
    }
    
    public final boolean component9() {
        return false;
    }
    
    public final boolean isEditing() {
        return false;
    }
    
    public final boolean component10() {
        return false;
    }
    
    public final boolean isCompleted() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final android.graphics.PointF component11() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final android.graphics.PointF getTextPosition() {
        return null;
    }
    
    public final double component12() {
        return 0.0;
    }
    
    public final double getArea() {
        return 0.0;
    }
    
    public final double component13() {
        return 0.0;
    }
    
    public final double getPerimeter() {
        return 0.0;
    }
    
    public final boolean component14() {
        return false;
    }
    
    public final boolean isDragging() {
        return false;
    }
    
    public final int component15() {
        return 0;
    }
    
    public final int getDraggedPointIndex() {
        return 0;
    }
}