package com.touptek.measurerealize.utils

import android.graphics.Matrix
import android.graphics.PointF
import android.util.Log
import android.view.MotionEvent
import com.touptek.measurerealize.TpImageView
import java.util.*
import kotlin.math.*

/**
 * 🎯 三点圆测量实例 - 专业级数据结构
 */
data class ThreeCircleMeasurement(
    val id: String = UUID.randomUUID().toString(),
    var viewPoints: MutableList<PointF> = mutableListOf(),  // [点1, 点2, 点3, 圆心] 视图坐标
    var bitmapPoints: MutableList<PointF> = mutableListOf(), // [点1, 点2, 点3, 圆心] 位图坐标
    var isSelected: Boolean = false,
    var isEditing: Boolean = false,
    var isCompleted: Boolean = false,
    var textPosition: PointF? = null,
    var creationTime: Long = System.currentTimeMillis(),
    var lastModified: Long = System.currentTimeMillis(),
    // 缓存计算结果以提高性能
    private var cachedRadius: Double? = null,
    private var cachedArea: Double? = null,
    private var cachedPerimeter: Double? = null,
    private var cacheValidTime: Long = 0L
) {
    companion object {
        private const val CACHE_VALIDITY_MS = 100L // 缓存有效期100ms
    }

    /**
     * 🎯 根据三个点计算圆心和半径 - 使用视图坐标进行显示计算
     */
    fun calculateCircleFromThreePoints(): Triple<PointF, Double, Boolean> {
        if (viewPoints.size < 3) return Triple(PointF(), 0.0, false)

        val p1 = viewPoints[0]
        val p2 = viewPoints[1]
        val p3 = viewPoints[2]
        
        // 检查三点是否共线
        val area = abs((p2.x - p1.x) * (p3.y - p1.y) - (p3.x - p1.x) * (p2.y - p1.y))
        if (area < 1e-6) {
            return Triple(PointF(), 0.0, false) // 三点共线，无法确定圆
        }
        
        // 计算圆心坐标
        val d = 2 * (p1.x * (p2.y - p3.y) + p2.x * (p3.y - p1.y) + p3.x * (p1.y - p2.y))
        if (abs(d) < 1e-6) {
            return Triple(PointF(), 0.0, false)
        }
        
        val ux = ((p1.x * p1.x + p1.y * p1.y) * (p2.y - p3.y) + 
                  (p2.x * p2.x + p2.y * p2.y) * (p3.y - p1.y) + 
                  (p3.x * p3.x + p3.y * p3.y) * (p1.y - p2.y)) / d
                  
        val uy = ((p1.x * p1.x + p1.y * p1.y) * (p3.x - p2.x) + 
                  (p2.x * p2.x + p2.y * p2.y) * (p1.x - p3.x) + 
                  (p3.x * p3.x + p3.y * p3.y) * (p2.x - p1.x)) / d
        
        val center = PointF(ux, uy)
        val radius = sqrt((center.x - p1.x).pow(2) + (center.y - p1.y).pow(2)).toDouble()

        return Triple(center, radius, true)
    }

    /**
     * 🎯 计算半径（使用位图坐标 - 真实半径，不受缩放影响）
     */
    fun calculateRadius(): Double {
        val currentTime = System.currentTimeMillis()
        if (cachedRadius != null && (currentTime - cacheValidTime) < CACHE_VALIDITY_MS) {
            return cachedRadius!!
        }

        if (bitmapPoints.size < 3) return 0.0

        val p1 = bitmapPoints[0]
        val p2 = bitmapPoints[1]
        val p3 = bitmapPoints[2]

        // 使用位图坐标计算圆心和半径
        val radius = calculateCircleFromBitmapPoints(p1, p2, p3)

        // 更新缓存
        cachedRadius = radius
        cacheValidTime = currentTime
        return radius
    }

    /**
     * 🎯 基于位图坐标计算圆的半径
     */
    private fun calculateCircleFromBitmapPoints(p1: PointF, p2: PointF, p3: PointF): Double {
        // 检查三点是否共线
        val area = 0.5 * abs((p2.x - p1.x) * (p3.y - p1.y) - (p3.x - p1.x) * (p2.y - p1.y))
        if (area < 1e-10) return 0.0 // 三点共线

        // 计算圆心坐标
        val d = 2 * (p1.x * (p2.y - p3.y) + p2.x * (p3.y - p1.y) + p3.x * (p1.y - p2.y))
        if (abs(d) < 1e-10) return 0.0 // 避免除零

        val ux = ((p1.x * p1.x + p1.y * p1.y) * (p2.y - p3.y) +
                  (p2.x * p2.x + p2.y * p2.y) * (p3.y - p1.y) +
                  (p3.x * p3.x + p3.y * p3.y) * (p1.y - p2.y)) / d
        val uy = ((p1.x * p1.x + p1.y * p1.y) * (p3.x - p2.x) +
                  (p2.x * p2.x + p2.y * p2.y) * (p1.x - p3.x) +
                  (p3.x * p3.x + p3.y * p3.y) * (p2.x - p1.x)) / d

        val center = PointF(ux, uy)
        return sqrt((center.x - p1.x).pow(2) + (center.y - p1.y).pow(2)).toDouble()
    }

    /**
     * 🎯 计算面积（使用缓存）
     */
    fun calculateArea(): Double {
        val currentTime = System.currentTimeMillis()
        if (cachedArea != null && (currentTime - cacheValidTime) < CACHE_VALIDITY_MS) {
            return cachedArea!!
        }

        val radius = calculateRadius()
        val area = if (radius > 0) PI * radius * radius else 0.0

        cachedArea = area
        return area
    }

    /**
     * 🎯 计算周长（使用缓存）
     */
    fun calculatePerimeter(): Double {
        val currentTime = System.currentTimeMillis()
        if (cachedPerimeter != null && (currentTime - cacheValidTime) < CACHE_VALIDITY_MS) {
            return cachedPerimeter!!
        }

        val radius = calculateRadius()
        val perimeter = if (radius > 0) 2.0 * PI * radius else 0.0

        cachedPerimeter = perimeter
        return perimeter
    }

    /**
     * ⭕ 计算圆周长
     */
    fun calculateCircumference(): Double {
        return calculatePerimeter() // 圆周长就是周长
    }

    /**
     * 🎯 检查点是否在触摸范围内
     */
    fun isPointInTouchRange(touchPoint: PointF, touchRadius: Float): Boolean {
        // 检查三个控制点
        for (i in 0 until minOf(3, viewPoints.size)) {
            val point = viewPoints[i]
            val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
            if (distance <= touchRadius) {
                return true
            }
        }
        
        // 检查圆心（如果已计算）
        if (viewPoints.size >= 4) {
            val center = viewPoints[3]
            val distance = sqrt((touchPoint.x - center.x).pow(2) + (touchPoint.y - center.y).pow(2))
            if (distance <= touchRadius) {
                return true
            }
        }
        
        return false
    }

    /**
     * 🎯 获取最近的点索引
     */
    fun getNearestPointIndex(touchPoint: PointF): Int {
        var nearestIndex = -1
        var minDistance = Float.MAX_VALUE
        
        // 检查三个控制点
        for (i in 0 until minOf(3, viewPoints.size)) {
            val point = viewPoints[i]
            val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
            if (distance < minDistance) {
                minDistance = distance
                nearestIndex = i
            }
        }
        
        // 检查圆心（索引为3）
        if (viewPoints.size >= 4) {
            val center = viewPoints[3]
            val distance = sqrt((touchPoint.x - center.x).pow(2) + (touchPoint.y - center.y).pow(2))
            if (distance < minDistance) {
                nearestIndex = 3
            }
        }
        
        return nearestIndex
    }

    /**
     * 🎯 更新三点圆约束
     */
    fun updateWithThreePointConstraint(pointIndex: Int, newPoint: PointF) {
        if (pointIndex < 0 || pointIndex >= viewPoints.size) return
        
        when (pointIndex) {
            0, 1, 2 -> {
                // 更新控制点
                viewPoints[pointIndex] = newPoint
                
                // 重新计算圆心
                val (center, _, isValid) = calculateCircleFromThreePoints()
                if (isValid && viewPoints.size >= 4) {
                    viewPoints[3] = center
                }
            }
            3 -> {
                // 拖拽圆心 - 整体移动
                if (viewPoints.size >= 4) {
                    val oldCenter = viewPoints[3]
                    val deltaX = newPoint.x - oldCenter.x
                    val deltaY = newPoint.y - oldCenter.y
                    
                    // 移动所有控制点
                    for (i in 0 until 3) {
                        viewPoints[i] = PointF(viewPoints[i].x + deltaX, viewPoints[i].y + deltaY)
                    }
                    
                    // 更新圆心
                    viewPoints[3] = newPoint
                }
            }
        }
        
        // 清除缓存
        markAsModified()
        invalidateCache()
    }

    /**
     * 🎯 同步位图坐标
     */
    fun syncBitmapCoords(imageView: TpImageView) {
        bitmapPoints.clear()
        viewPoints.forEach { viewPoint ->
            val bitmapPoint = convertViewToBitmapCoords(viewPoint, imageView)
            bitmapPoints.add(bitmapPoint)
        }
    }

    /**
     * 🎯 同步视图坐标
     */
    fun syncViewCoords(imageView: TpImageView) {
        viewPoints.clear()
        bitmapPoints.forEach { bitmapPoint ->
            val viewPoint = convertBitmapToViewCoords(bitmapPoint, imageView)
            viewPoints.add(viewPoint)
        }
        invalidateCache()
    }

    /**
     * 🎯 坐标转换：视图坐标 -> 位图坐标
     */
    private fun convertViewToBitmapCoords(viewPoint: PointF, imageView: TpImageView): PointF {
        val matrix = imageView.imageMatrix
        val inverseMatrix = Matrix()
        return if (matrix.invert(inverseMatrix)) {
            val point = FloatArray(2) { 0f }
            point[0] = viewPoint.x
            point[1] = viewPoint.y
            inverseMatrix.mapPoints(point)
            PointF(point[0], point[1])
        } else {
            PointF(viewPoint.x, viewPoint.y)
        }
    }

    /**
     * 🎯 坐标转换：位图坐标 -> 视图坐标
     */
    private fun convertBitmapToViewCoords(bitmapPoint: PointF, imageView: TpImageView): PointF {
        val matrix = imageView.imageMatrix
        val point = FloatArray(2) { 0f }
        point[0] = bitmapPoint.x
        point[1] = bitmapPoint.y
        matrix.mapPoints(point)
        return PointF(point[0], point[1])
    }

    /**
     * 🎯 计算文本显示位置 - 避免遮挡圆心标记
     */
    fun calculateTextPosition(): PointF {
        val (center, radius, isValid) = calculateCircleFromThreePoints()
        return if (isValid) {
            // 文本显示在圆心下方，避免遮挡圆心标记
            val textOffsetY = maxOf(80f, radius.toFloat() * 0.3f) // 至少80像素偏移，或半径的30%
            PointF(center.x, center.y + textOffsetY)
        } else {
            // 如果无法计算圆心，使用三个点的重心下方
            if (viewPoints.size >= 3) {
                val centerX = (viewPoints[0].x + viewPoints[1].x + viewPoints[2].x) / 3f
                val centerY = (viewPoints[0].y + viewPoints[1].y + viewPoints[2].y) / 3f
                PointF(centerX, centerY + 80f) // 重心下方80像素
            } else {
                PointF()
            }
        }
    }

    /**
     * 更新最后修改时间
     */
    private fun markAsModified() {
        lastModified = System.currentTimeMillis()
    }

    /**
     * 清除缓存
     */
    private fun invalidateCache() {
        cachedRadius = null
        cachedArea = null
        cachedPerimeter = null
        cacheValidTime = 0L
    }
}

/**
 * 🎯 三点圆测量助手 - 专业级测量管理类
 */
class ThreeCircleMeasureHelper {
    companion object {
        private const val TAG = "ThreeCircleMeasureHelper"
        private const val TOUCH_RADIUS = 80f           // 触摸检测半径
        private const val LONG_PRESS_DURATION = 800L   // 长按删除时间
        private const val CLICK_TOLERANCE = 20f        // 点击容差
    }

    // 核心数据
    private val measurements = mutableListOf<ThreeCircleMeasurement>()
    private lateinit var imageView: TpImageView
    private var measurementUpdateCallback: (() -> Unit)? = null

    // 交互状态管理
    private var selectedMeasurement: ThreeCircleMeasurement? = null
    private var isDraggingPoint = false
    private var draggedPointIndex = -1
    private var longPressStartTime = 0L
    private var lastTouchX = 0f
    private var lastTouchY = 0f

    /**
     * 🎯 初始化助手
     */
    fun init(imageView: TpImageView, bitmap: android.graphics.Bitmap) {
        this.imageView = imageView
        Log.d(TAG, "🚀 ThreeCircleMeasureHelper initialized")
    }

    /**
     * 🎯 设置测量更新回调
     */
    fun setMeasurementUpdateCallback(callback: () -> Unit) {
        this.measurementUpdateCallback = callback
    }

    /**
     * 🎯 开始新的三点圆测量 - 在屏幕中央创建三点圆
     */
    fun startNewMeasurement(): String {
        // 在视图中心生成三点圆
        val viewCenterX = imageView.width / 2f
        val viewCenterY = imageView.height / 2f
        val triangleSize = minOf(imageView.width, imageView.height) / 6f

        // 创建三个控制点（等边三角形分布）
        val point1 = PointF(viewCenterX, viewCenterY - triangleSize * 0.6f)
        val point2 = PointF(viewCenterX - triangleSize * 0.8f, viewCenterY + triangleSize * 0.4f)
        val point3 = PointF(viewCenterX + triangleSize * 0.8f, viewCenterY + triangleSize * 0.4f)

        // 创建完整的测量
        val measurement = ThreeCircleMeasurement(
            isSelected = true,
            isEditing = false,  // 直接设为完成状态，可拖动
            isCompleted = true
        )

        // 添加视图坐标点（三个控制点）
        measurement.viewPoints.addAll(listOf(point1, point2, point3))

        // 计算并添加圆心
        val (center, _, isValid) = measurement.calculateCircleFromThreePoints()
        if (isValid) {
            measurement.viewPoints.add(center)
        } else {
            // 如果无法计算圆心，添加重心作为圆心
            val centerX = (point1.x + point2.x + point3.x) / 3f
            val centerY = (point1.y + point2.y + point3.y) / 3f
            measurement.viewPoints.add(PointF(centerX, centerY))
        }

        // 同步位图坐标
        measurement.syncBitmapCoords(imageView)

        // 设置文本位置
        measurement.textPosition = measurement.calculateTextPosition()

        // 取消其他测量的选中状态
        measurements.forEach {
            it.isSelected = false
            it.isEditing = false
        }

        measurements.add(measurement)
        selectedMeasurement = measurement

        notifyUpdate()
        Log.d(TAG, "🎯 Generated three-point circle in view coordinates - radius: ${String.format("%.1f", measurement.calculateRadius())}px")
        return measurement.id
    }

    /**
     * 🎯 处理触摸事件
     */
    fun handleTouchEvent(event: MotionEvent, viewWidth: Int, viewHeight: Int): Boolean {
        val x = event.x
        val y = event.y
        val touchPoint = PointF(x, y)

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                return handleTouchDown(touchPoint, viewWidth, viewHeight)
            }
            MotionEvent.ACTION_MOVE -> {
                return handleTouchMove(touchPoint)
            }
            MotionEvent.ACTION_UP -> {
                return handleTouchUp(touchPoint, viewWidth, viewHeight)
            }
        }
        return false
    }

    /**
     * 🎯 处理触摸按下事件
     */
    private fun handleTouchDown(touchPoint: PointF, viewWidth: Int, viewHeight: Int): Boolean {
        // 记录触摸起始信息
        longPressStartTime = System.currentTimeMillis()
        lastTouchX = touchPoint.x
        lastTouchY = touchPoint.y

        // 检查端点触摸（优先级最高）
        for (measurement in measurements.reversed()) {
            if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                val nearestPointIndex = measurement.getNearestPointIndex(touchPoint)
                if (nearestPointIndex >= 0) {
                    selectedMeasurement = measurement
                    draggedPointIndex = nearestPointIndex
                    isDraggingPoint = true

                    measurements.forEach { it.isSelected = false }
                    measurement.isSelected = true

                    val pointName = when(nearestPointIndex) {
                        0, 1, 2 -> "control point"
                        3 -> "center"
                        else -> "unknown"
                    }
                    Log.d(TAG, "🎯 Started dragging $pointName (index $nearestPointIndex) of measurement ${measurement.id}")
                    notifyUpdate()
                    return true
                }
            }
        }

        // 空白区域点击处理
        if (isInImageContentArea(touchPoint, viewWidth, viewHeight)) {
            measurements.forEach { it.isSelected = false }
            selectedMeasurement = null
            Log.d(TAG, "🎯 Clicked in empty area, cleared selection")
            notifyUpdate()
        }

        return false
    }

    /**
     * 🎯 处理触摸移动事件
     */
    private fun handleTouchMove(touchPoint: PointF): Boolean {
        if (isDraggingPoint && selectedMeasurement != null && draggedPointIndex >= 0) {
            // 正常拖拽处理
            selectedMeasurement!!.updateWithThreePointConstraint(draggedPointIndex, touchPoint)
            selectedMeasurement!!.syncBitmapCoords(imageView)
            notifyUpdate()
            return true
        } else if (!isDraggingPoint && selectedMeasurement != null) {
            // 触摸恢复机制
            for (measurement in measurements.reversed()) {
                if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS) && measurement == selectedMeasurement) {
                    draggedPointIndex = measurement.getNearestPointIndex(touchPoint)
                    isDraggingPoint = true
                    // 立即处理这次移动
                    selectedMeasurement!!.updateWithThreePointConstraint(draggedPointIndex, touchPoint)
                    selectedMeasurement!!.syncBitmapCoords(imageView)
                    notifyUpdate()
                    Log.d(TAG, "🔄 Touch recovery successful - resumed dragging point $draggedPointIndex")
                    return true
                }
            }
        }
        return false
    }

    /**
     * 🎯 处理触摸抬起事件
     */
    private fun handleTouchUp(touchPoint: PointF, viewWidth: Int, viewHeight: Int): Boolean {
        val touchDuration = System.currentTimeMillis() - longPressStartTime
        val touchDistance = sqrt((touchPoint.x - lastTouchX).pow(2) + (touchPoint.y - lastTouchY).pow(2))
        val wasDragging = isDraggingPoint

        var handled = false

        // 重置拖拽状态
        isDraggingPoint = false
        draggedPointIndex = -1

        if (wasDragging) {
            // 拖拽完成
            notifyUpdate()
            handled = true
            Log.d(TAG, "🎯 Finished dragging")
        }

        // 长按删除
        if (!wasDragging && touchDistance < CLICK_TOLERANCE && touchDuration > LONG_PRESS_DURATION) {
            for (measurement in measurements.reversed()) {
                if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                    measurements.remove(measurement)
                    if (selectedMeasurement == measurement) {
                        selectedMeasurement = null
                    }
                    notifyUpdate()
                    handled = true
                    Log.d(TAG, "🗑️ Long press delete - measurement ${measurement.id} removed")
                    break
                }
            }
        }

        // 轻触选中
        if (!wasDragging && !handled && touchDistance < CLICK_TOLERANCE && touchDuration < LONG_PRESS_DURATION) {
            for (measurement in measurements.reversed()) {
                if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                    measurements.forEach { it.isSelected = false }
                    measurement.isSelected = true
                    selectedMeasurement = measurement
                    notifyUpdate()
                    Log.d(TAG, "🎯 Selected measurement: ${measurement.id}")
                    return true
                }
            }
        }

        return handled
    }

    /**
     * 🎯 智能UI区域保护 - 避免UI按钮区域被误判
     */
    private fun isInImageContentArea(touchPoint: PointF, viewWidth: Int, viewHeight: Int): Boolean {
        try {
            // 定义UI区域边界（顶部和底部各20%为UI区域）
            val topUIHeight = viewHeight * 0.2f
            val bottomUIStart = viewHeight * 0.8f

            // 如果触摸点在顶部或底部UI区域，不取消选中
            if (touchPoint.y < topUIHeight || touchPoint.y > bottomUIStart) {
                Log.d(TAG, "🛡️ Touch in UI area: y=${touchPoint.y}, topUI=$topUIHeight, bottomUI=$bottomUIStart")
                return false
            }

            // 中间区域认为是图像内容区域
            Log.d(TAG, "📍 Touch in image content area: y=${touchPoint.y}")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error in isInImageContentArea: ${e.message}")
            // 出错时保守处理，不清除选中状态
            return false
        }
    }

    /**
     * 🎯 获取所有测量数据用于覆盖层显示
     */
    fun getAllMeasurementData(): List<ThreeCircleMeasurement> {
        return measurements.toList()
    }

    /**
     * 🎯 获取测量数量
     */
    fun getMeasurementCount(): Int = measurements.size

    /**
     * 🎯 检查是否有选中的测量
     */
    fun hasSelectedMeasurement(): Boolean = selectedMeasurement != null

    /**
     * 🎯 获取选中的测量
     */
    fun getSelectedMeasurement(): ThreeCircleMeasurement? = selectedMeasurement

    /**
     * 🎯 检查是否正在拖拽点
     */
    fun isDraggingPoint(): Boolean = isDraggingPoint

    /**
     * 🎯 检查是否靠近任何测量
     */
    fun isNearAnyMeasurement(touchPoint: PointF): Boolean {
        return measurements.any { measurement ->
            measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)
        }
    }

    /**
     * 🗑️ 删除选中的测量 - 智能删除逻辑
     */
    fun deleteSelectedMeasurement(): Boolean {
        var selected = selectedMeasurement

        // 🔄 如果没有选中的测量，尝试自动选中最后一个测量
        if (selected == null && measurements.isNotEmpty()) {
            val lastMeasurement = measurements.last()
            lastMeasurement.isSelected = true
            selectedMeasurement = lastMeasurement
            selected = lastMeasurement
            Log.d(TAG, "🔄 Auto-selected last measurement for deletion: ${lastMeasurement.id}")
        }

        if (selected == null) {
            Log.w(TAG, "⚠️ No measurements available for deletion")
            return false
        }

        val removed = measurements.remove(selected)
        if (removed) {
            selectedMeasurement = null
            resetInteractionState()

            // 如果还有其他测量，选中最后一个
            if (measurements.isNotEmpty()) {
                val lastMeasurement = measurements.last()
                lastMeasurement.isSelected = true
                selectedMeasurement = lastMeasurement
                Log.d(TAG, "🎯 Auto-selected last measurement: ${lastMeasurement.id}")
            }

            notifyUpdate()
            Log.d(TAG, "✅ Measurement deleted successfully: ${selected.id}")
            return true
        }
        return false
    }

    /**
     * 🔄 清除所有选中状态
     */
    fun clearSelection() {
        measurements.forEach { it.isSelected = false }
        selectedMeasurement = null
        resetInteractionState()
        notifyUpdate()
        Log.d(TAG, "🔄 All selections cleared")
    }

    /**
     * 🔄 重置交互状态
     */
    private fun resetInteractionState() {
        isDraggingPoint = false
        draggedPointIndex = -1
        longPressStartTime = 0L
    }

    /**
     * 🎯 暂停测量 - 保持数据但停止交互
     */
    fun pauseMeasurement() {
        resetInteractionState()
        Log.d(TAG, "⏸️ Measurement paused - data preserved")
    }

    /**
     * 🎯 恢复测量 - 从暂停状态恢复到可编辑状态
     */
    fun resumeMeasurement() {
        Log.d(TAG, "▶️ Measurement resumed - ready for continued editing")
    }

    /**
     * 🎯 缩放变化时同步坐标
     */
    fun onScaleChanged() {
        // 从位图坐标恢复视图坐标，确保测量跟随图像
        measurements.forEach { measurement ->
            measurement.syncViewCoords(imageView)
        }
        notifyUpdate()
        Log.d(TAG, "🔄 Synced coordinates after scale change")
    }

    /**
     * 🎯 通知更新
     */
    private fun notifyUpdate() {
        measurementUpdateCallback?.invoke()
    }

    /**
     * 🎯 停止测量
     */
    fun stopMeasurement() {
        resetInteractionState()
        selectedMeasurement = null
        Log.d(TAG, "🛑 Three circle measurement stopped")
    }

    /**
     * 🎯 清除所有测量数据
     */
    fun clearAllMeasurements() {
        measurements.clear()
        selectedMeasurement = null
        resetInteractionState()
        notifyUpdate()
        Log.d(TAG, "🗑️ All measurements cleared")
    }
}
