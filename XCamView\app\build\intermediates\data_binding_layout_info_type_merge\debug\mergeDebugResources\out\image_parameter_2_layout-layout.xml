<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="image_parameter_2_layout" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\image_parameter_2_layout.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/image_parameter_2_layout_0" view="LinearLayout"><Expressions/><location startLine="0" startOffset="0" endLine="176" endOffset="14"/></Target><Target id="@+id/text_sharpness_value" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="42" endOffset="41"/></Target><Target id="@+id/btn_sharpness_reduce" view="ImageButton"><Expressions/><location startLine="58" startOffset="12" endLine="64" endOffset="47"/></Target><Target id="@+id/seekbar_sharpness_tv" view="SeekBar"><Expressions/><location startLine="66" startOffset="12" endLine="75" endOffset="57"/></Target><Target id="@+id/btn_sharpness_add" view="ImageButton"><Expressions/><location startLine="84" startOffset="12" endLine="90" endOffset="47"/></Target><Target id="@+id/text_denoise_value" view="TextView"><Expressions/><location startLine="114" startOffset="12" endLine="122" endOffset="41"/></Target><Target id="@+id/btn_denoise_reduce" view="ImageButton"><Expressions/><location startLine="131" startOffset="12" endLine="137" endOffset="47"/></Target><Target id="@+id/seekbar_denoise_tv" view="SeekBar"><Expressions/><location startLine="139" startOffset="12" endLine="148" endOffset="57"/></Target><Target id="@+id/btn_denoise_add" view="ImageButton"><Expressions/><location startLine="150" startOffset="12" endLine="156" endOffset="47"/></Target><Target id="@+id/btn_Default_image_parameter_2" view="Button"><Expressions/><location startLine="167" startOffset="8" endLine="171" endOffset="36"/></Target></Targets></Layout>