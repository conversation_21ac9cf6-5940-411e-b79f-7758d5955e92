<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_margin="8dp"
    android:background="@drawable/rounded_border"
    android:orientation="vertical"
    android:padding="16dp">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:gravity="center"
        android:orientation="vertical">


        <RadioGroup
            android:id="@+id/wb_btn_group"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/radio_auto_tv"
                android:layout_width="125dp"
                android:layout_height="wrap_content"
                android:button="@drawable/tp_custom_radionbutton"
                android:text="Auto"
                android:textColor="@color/colorISPText" />

            <RadioButton
                android:id="@+id/radio_manual_tv"
                android:layout_width="125dp"
                android:layout_height="wrap_content"
                android:button="@drawable/tp_custom_radionbutton"
                android:text="Manual"
                android:textColor="@color/colorISPText" />

            <RadioButton
                android:id="@+id/radio_roi_tv"
                android:layout_width="125dp"
                android:layout_height="wrap_content"
                android:button="@drawable/tp_custom_radionbutton"
                android:text="ROI"
                android:textColor="@color/colorISPText" />
        </RadioGroup>
    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="307dp"
                android:layout_height="match_parent"
                android:text="@string/title_wb_red_text"
                android:textColor="@color/colorISPText"
                android:textSize="18sp" />

            <TextView
                android:id="@+id/text_red_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="80"
                android:textAlignment="viewEnd"
                android:textColor="@color/colorISPText"
                android:textSize="18sp" />
        </LinearLayout>

        <LinearLayout

            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageButton
                android:id="@+id/btn_red_reduce"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/sub_n" />

            <SeekBar
                android:id="@+id/seekbar_red_tv"
                android:layout_width="255dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:progressBackgroundTint="@color/grey_background"
                android:progressTint="@color/colorISPBlue"
                android:thumbTint="@color/colorISPBlue" />

            <ImageButton
                android:id="@+id/btn_red_add"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/add_n" />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="307dp"
                android:layout_height="match_parent"
                android:text="@string/title_wb_green_text"
                android:textColor="@color/colorISPText"
                android:textSize="18sp" />

            <TextView
                android:id="@+id/text_green_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="80"
                android:textAlignment="viewEnd"
                android:textColor="@color/colorISPText"
                android:textSize="18sp" />
        </LinearLayout>

        <LinearLayout

            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageButton
                android:id="@+id/btn_green_reduce"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/sub_n" />

            <SeekBar
                android:id="@+id/seekbar_green_tv"
                android:layout_width="255dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:progressBackgroundTint="@color/grey_background"
                android:progressTint="@color/colorISPBlue"
                android:thumbTint="@color/colorISPBlue" />

            <ImageButton
                android:id="@+id/btn_green_add"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/add_n" />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="307dp"
                android:layout_height="match_parent"
                android:text="@string/title_wb_blue_text"
                android:textColor="@color/colorISPText"
                android:textSize="18sp" />

            <TextView
                android:id="@+id/text_blue_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="80"
                android:textAlignment="viewEnd"
                android:textColor="@color/colorISPText"
                android:textSize="18sp" />
        </LinearLayout>

        <LinearLayout

            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageButton
                android:id="@+id/btn_blue_reduce"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/sub_n" />

            <SeekBar
                android:id="@+id/seekbar_blue_tv"
                android:layout_width="255dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:progressBackgroundTint="@color/grey_background"
                android:progressTint="@color/colorISPBlue"
                android:thumbTint="@color/colorISPBlue" />

            <ImageButton
                android:id="@+id/btn_blue_add"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/add_n" />
        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:gravity="center"
        android:orientation="vertical">

        <Button
            android:id="@+id/btn_Default_wb"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:text="Default" />
    </LinearLayout>


</LinearLayout>