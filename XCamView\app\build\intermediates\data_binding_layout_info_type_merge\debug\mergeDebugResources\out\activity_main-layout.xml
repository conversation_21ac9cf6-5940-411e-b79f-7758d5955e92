<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout" rootNodeViewId="@+id/root_view"><Targets><Target id="@+id/root_view" tag="layout/activity_main_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="80" endOffset="16"/></Target><Target id="@+id/main_center_info_label" view="TextView"><Expressions/><location startLine="9" startOffset="4" endLine="28" endOffset="47"/></Target><Target id="@+id/blue_texture_view" view="TextureView"><Expressions/><location startLine="30" startOffset="4" endLine="35" endOffset="45"/></Target><Target id="@+id/roi_view" view="com.touptek.ui.TpRoiView"><Expressions/><location startLine="38" startOffset="4" endLine="42" endOffset="40"/></Target><Target id="@+id/measurement_view" view="com.touptek.xcamview.view.MeasurementOverlayView"><Expressions/><location startLine="45" startOffset="4" endLine="49" endOffset="40"/></Target><Target id="@+id/tv_timer" view="TextView"><Expressions/><location startLine="51" startOffset="4" endLine="60" endOffset="34"/></Target><Target id="@+id/tv_view" view="TextureView"><Expressions/><location startLine="62" startOffset="4" endLine="67" endOffset="45"/></Target><Target id="@+id/tv_duration" view="TextView"><Expressions/><location startLine="71" startOffset="4" endLine="78" endOffset="34"/></Target></Targets></Layout>