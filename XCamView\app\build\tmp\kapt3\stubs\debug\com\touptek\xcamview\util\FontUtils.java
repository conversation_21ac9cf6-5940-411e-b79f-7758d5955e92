package com.touptek.xcamview.util;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\tJ\u0010\u0010\u0010\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u0012H\u0002J\u0006\u0010\u0013\u001a\u00020\u0004J\u0010\u0010\u0014\u001a\u00020\u000b2\u0006\u0010\u0015\u001a\u00020\u0016H\u0002J\u0010\u0010\u0017\u001a\u00020\u000b2\u0006\u0010\u0015\u001a\u00020\u0016H\u0002J\u000e\u0010\u0018\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\tJ\u0016\u0010\u0019\u001a\u00020\u000e2\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u001a\u001a\u00020\u0004J\u000e\u0010\u001b\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\tJ\b\u0010\u001c\u001a\u00020\u000eH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\n\u001a\u0004\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001d"}, d2 = {"Lcom/touptek/xcamview/util/FontUtils;", "", "()V", "FONT_KAI", "", "FONT_SONG", "FONT_SYSTEM", "activities", "", "Landroidx/appcompat/app/AppCompatActivity;", "appTypeface", "Landroid/graphics/Typeface;", "currentFont", "applyFontToActivity", "", "activity", "applyFontToViewGroup", "view", "Landroid/view/View;", "getCurrentFontType", "getKaiTypeface", "context", "Landroid/content/Context;", "getSongTypeface", "registerActivity", "setAppFont", "fontType", "unregisterActivity", "updateAllActivities", "app_debug"})
public final class FontUtils {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.xcamview.util.FontUtils INSTANCE = null;
    public static final int FONT_SYSTEM = 0;
    public static final int FONT_SONG = 1;
    public static final int FONT_KAI = 2;
    private static int currentFont = 0;
    private static android.graphics.Typeface appTypeface;
    private static final java.util.List<androidx.appcompat.app.AppCompatActivity> activities = null;
    
    private FontUtils() {
        super();
    }
    
    public final void setAppFont(@org.jetbrains.annotations.NotNull
    android.content.Context context, int fontType) {
    }
    
    public final void registerActivity(@org.jetbrains.annotations.NotNull
    androidx.appcompat.app.AppCompatActivity activity) {
    }
    
    public final void unregisterActivity(@org.jetbrains.annotations.NotNull
    androidx.appcompat.app.AppCompatActivity activity) {
    }
    
    private final void updateAllActivities() {
    }
    
    public final void applyFontToActivity(@org.jetbrains.annotations.NotNull
    androidx.appcompat.app.AppCompatActivity activity) {
    }
    
    private final void applyFontToViewGroup(android.view.View view) {
    }
    
    public final int getCurrentFontType() {
        return 0;
    }
    
    private final android.graphics.Typeface getSongTypeface(android.content.Context context) {
        return null;
    }
    
    private final android.graphics.Typeface getKaiTypeface(android.content.Context context) {
        return null;
    }
}