package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 🎨 专业级四点测角度助手主类 - 与XCamView架构完全一致
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000r\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b!\u0018\u0000 R2\u00020\u0001:\u0001RB\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u001a\u001a\u00020\u001bJ0\u0010\u001c\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u001f\u0012\u0004\u0012\u00020\u001f0\u001e0\u001d2\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u001f0\u001d2\b\u0010!\u001a\u0004\u0018\u00010\u001fJ(\u0010\"\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u001f\u0012\u0004\u0012\u00020\u001f0\u001e0\u001d2\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u001f0\u001dH\u0002J.\u0010#\u001a\u0010\u0012\u0004\u0012\u00020\u001f\u0012\u0004\u0012\u00020\u001f\u0018\u00010\u001e2\u0006\u0010$\u001a\u00020\u001f2\u0006\u0010%\u001a\u00020\u001f2\u0006\u0010!\u001a\u00020\u001fH\u0002J0\u0010&\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u001f\u0012\u0004\u0012\u00020\u001f0\u001e0\u001d2\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u001f0\u001d2\u0006\u0010!\u001a\u00020\u001fH\u0002J\u0006\u0010\'\u001a\u00020\u0014J\u0006\u0010(\u001a\u00020\u0014J\u0006\u0010)\u001a\u00020\nJ\f\u0010*\u001a\b\u0012\u0004\u0012\u00020+0\u001dJ\u0006\u0010,\u001a\u00020\u0006J\b\u0010-\u001a\u0004\u0018\u00010+J\u0010\u0010.\u001a\u00020\n2\u0006\u0010/\u001a\u00020\u001fH\u0002J\u001e\u00100\u001a\u00020\n2\u0006\u00101\u001a\u0002022\u0006\u00103\u001a\u00020\u00062\u0006\u00104\u001a\u00020\u0006J\u0006\u00105\u001a\u00020\nJ\u0016\u00106\u001a\u00020\u00142\u0006\u0010\u0007\u001a\u00020\b2\u0006\u00107\u001a\u00020\u0018J\u0006\u0010\u000b\u001a\u00020\nJ \u00108\u001a\u00020\n2\u0006\u0010/\u001a\u00020\u001f2\u0006\u00103\u001a\u00020\u00062\u0006\u00104\u001a\u00020\u0006H\u0002J\u000e\u00109\u001a\u00020\n2\u0006\u0010/\u001a\u00020\u001fJ \u0010:\u001a\u00020\n2\u0006\u0010;\u001a\u00020\u001f2\u0006\u0010<\u001a\u00020\u001f2\u0006\u0010=\u001a\u00020\u001fH\u0002J*\u0010>\u001a\u00020\n2\u0006\u0010;\u001a\u00020\u001f2\u0006\u0010$\u001a\u00020\u001f2\u0006\u0010%\u001a\u00020\u001f2\b\b\u0002\u0010?\u001a\u00020\u000eH\u0002J\u000e\u0010@\u001a\u00020\n2\u0006\u0010/\u001a\u00020\u001fJ\u000e\u0010A\u001a\u00020\n2\u0006\u0010/\u001a\u00020\u001fJ\u001a\u0010B\u001a\u0004\u0018\u00010\u001f2\u0006\u0010C\u001a\u00020\u000e2\u0006\u0010D\u001a\u00020\u000eH\u0002J\b\u0010E\u001a\u00020\u0014H\u0002J\u0006\u0010F\u001a\u00020\u0014J\u0006\u0010G\u001a\u00020\u0014J\u0006\u0010H\u001a\u00020\u0014J\b\u0010I\u001a\u00020\u0014H\u0002J\u0006\u0010J\u001a\u00020\u0014J\u0010\u0010K\u001a\u00020\u00142\u0006\u0010L\u001a\u00020\u0004H\u0002J\u0014\u0010M\u001a\u00020\u00142\f\u0010N\u001a\b\u0012\u0004\u0012\u00020\u00140\u0013J\u0018\u0010O\u001a\u00020\u00142\u0006\u0010L\u001a\u00020\u00042\u0006\u0010P\u001a\u00020\u0006H\u0002J\u0006\u0010Q\u001a\u00020\u001bR\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0012\u001a\n\u0012\u0004\u0012\u00020\u0014\u0018\u00010\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00040\u0016X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0019\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006S"}, d2 = {"Lcom/touptek/measurerealize/utils/FourPointAngleHelper;", "", "()V", "activeMeasurement", "Lcom/touptek/measurerealize/utils/FourPointAngleMeasurement;", "draggedPointIndex", "", "imageView", "Landroid/widget/ImageView;", "isCreatingNew", "", "isDraggingPoint", "isDraggingText", "lastTouchX", "", "lastTouchY", "longPressStartTime", "", "measurementUpdateCallback", "Lkotlin/Function0;", "", "measurements", "Ljava/util/ArrayList;", "originalBitmap", "Landroid/graphics/Bitmap;", "selectedMeasurement", "addNewMeasurement", "", "calculateExtensionLines", "", "Lkotlin/Pair;", "Landroid/graphics/PointF;", "points", "intersection", "calculateFallbackExtensionLines", "calculateLineExtension", "lineStart", "lineEnd", "calculateSmartExtensionLines", "clearAllMeasurements", "clearSelection", "deleteSelectedMeasurement", "getAllMeasurementData", "Lcom/touptek/measurerealize/utils/FourPointAngleMeasurementData;", "getMeasurementCount", "getMeasurementData", "handleLongPress", "touchPoint", "handleTouchEvent", "event", "Landroid/view/MotionEvent;", "viewWidth", "viewHeight", "hasSelectedMeasurement", "init", "bitmap", "isInImageContentArea", "isNearAnyMeasurement", "isPointBetweenPoints", "point", "start", "end", "isPointOnLineSegment", "tolerance", "isPointOnMeasurement", "isTouchingMeasurementPoint", "normalizeVector", "x", "y", "notifyUpdate", "onScaleChanged", "pauseMeasurement", "reset", "resetInteractionState", "resumeMeasurement", "selectMeasurement", "measurement", "setMeasurementUpdateCallback", "callback", "startDraggingPoint", "pointIndex", "startNewMeasurement", "Companion", "app_debug"})
public final class FourPointAngleHelper {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.measurerealize.utils.FourPointAngleHelper.Companion Companion = null;
    private static final java.lang.String TAG = "FourPointAngleHelper";
    private static final float TOUCH_RADIUS = 80.0F;
    private static final float CLICK_TOLERANCE = 20.0F;
    private static final long LONG_PRESS_DURATION = 800L;
    private android.widget.ImageView imageView;
    private android.graphics.Bitmap originalBitmap;
    private final java.util.ArrayList<com.touptek.measurerealize.utils.FourPointAngleMeasurement> measurements = null;
    private com.touptek.measurerealize.utils.FourPointAngleMeasurement activeMeasurement;
    private com.touptek.measurerealize.utils.FourPointAngleMeasurement selectedMeasurement;
    private boolean isDraggingPoint = false;
    private boolean isDraggingText = false;
    private int draggedPointIndex = -1;
    private boolean isCreatingNew = false;
    private float lastTouchX = 0.0F;
    private float lastTouchY = 0.0F;
    private long longPressStartTime = 0L;
    private kotlin.jvm.functions.Function0<kotlin.Unit> measurementUpdateCallback;
    
    public FourPointAngleHelper() {
        super();
    }
    
    /**
     * 🚀 初始化助手 - 与AngleMeasureHelper保持一致
     */
    public final void init(@org.jetbrains.annotations.NotNull
    android.widget.ImageView imageView, @org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
    }
    
    private final void resetInteractionState() {
    }
    
    /**
     * 🎯 开始新的四点角度测量 - 在屏幕中心生成默认角度
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String startNewMeasurement() {
        return null;
    }
    
    /**
     * 🔔 通知更新
     */
    private final void notifyUpdate() {
    }
    
    /**
     * 🎯 设置测量更新回调
     */
    public final void setMeasurementUpdateCallback(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    /**
     * 📊 获取所有测量数据用于覆盖层显示
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.touptek.measurerealize.utils.FourPointAngleMeasurementData> getAllMeasurementData() {
        return null;
    }
    
    /**
     * 🎨 获取当前选中的测量数据（向后兼容）
     */
    @org.jetbrains.annotations.Nullable
    public final com.touptek.measurerealize.utils.FourPointAngleMeasurementData getMeasurementData() {
        return null;
    }
    
    /**
     * 🎯 处理触摸事件 - 与AngleMeasureHelper保持一致的交互逻辑
     */
    public final boolean handleTouchEvent(@org.jetbrains.annotations.NotNull
    android.view.MotionEvent event, int viewWidth, int viewHeight) {
        return false;
    }
    
    private final void selectMeasurement(com.touptek.measurerealize.utils.FourPointAngleMeasurement measurement) {
    }
    
    private final void startDraggingPoint(com.touptek.measurerealize.utils.FourPointAngleMeasurement measurement, int pointIndex) {
    }
    
    private final boolean handleLongPress(android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🔄 缩放变化时同步坐标
     */
    public final void onScaleChanged() {
    }
    
    /**
     * 🎯 获取测量数量
     */
    public final int getMeasurementCount() {
        return 0;
    }
    
    /**
     * 🎯 检查是否正在拖拽点
     */
    public final boolean isDraggingPoint() {
        return false;
    }
    
    /**
     * 🎯 检查是否触摸到测量点
     */
    public final boolean isTouchingMeasurementPoint(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🎯 添加新的四点角度测量 - 与AngleMeasureHelper保持一致
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String addNewMeasurement() {
        return null;
    }
    
    /**
     * 🗑️ 删除选中的测量
     */
    public final boolean deleteSelectedMeasurement() {
        return false;
    }
    
    /**
     * ⏸️ 暂停测量
     */
    public final void pauseMeasurement() {
    }
    
    /**
     * ▶️ 恢复测量
     */
    public final void resumeMeasurement() {
    }
    
    /**
     * 🧹 清除所有测量
     */
    public final void clearAllMeasurements() {
    }
    
    /**
     * 🧹 重置助手
     */
    public final void reset() {
    }
    
    /**
     * 🎨 计算智能延长线 - 当线段不相交时自动延长使其相交
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<kotlin.Pair<android.graphics.PointF, android.graphics.PointF>> calculateExtensionLines(@org.jetbrains.annotations.NotNull
    java.util.List<? extends android.graphics.PointF> points, @org.jetbrains.annotations.Nullable
    android.graphics.PointF intersection) {
        return null;
    }
    
    /**
     * 🎯 计算智能延长线 - 只延长到交点，不超过
     */
    private final java.util.List<kotlin.Pair<android.graphics.PointF, android.graphics.PointF>> calculateSmartExtensionLines(java.util.List<? extends android.graphics.PointF> points, android.graphics.PointF intersection) {
        return null;
    }
    
    /**
     * 🎯 检查触摸点是否在任何测量上（用于智能模式激活）
     */
    public final boolean isPointOnMeasurement(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🔍 检查是否有选中的测量
     */
    public final boolean hasSelectedMeasurement() {
        return false;
    }
    
    /**
     * 🔍 检查触摸点是否靠近任何测量
     */
    public final boolean isNearAnyMeasurement(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🔄 清除所有选中状态（用于模式切换时避免冲突）
     */
    public final void clearSelection() {
    }
    
    /**
     * 🔧 计算单条线段的延长线 - 只延长到交点
     */
    private final kotlin.Pair<android.graphics.PointF, android.graphics.PointF> calculateLineExtension(android.graphics.PointF lineStart, android.graphics.PointF lineEnd, android.graphics.PointF intersection) {
        return null;
    }
    
    /**
     * 🔍 检查点是否在线段上
     */
    private final boolean isPointOnLineSegment(android.graphics.PointF point, android.graphics.PointF lineStart, android.graphics.PointF lineEnd, float tolerance) {
        return false;
    }
    
    /**
     * 🔍 检查交点是否在两个端点之间
     */
    private final boolean isPointBetweenPoints(android.graphics.PointF point, android.graphics.PointF start, android.graphics.PointF end) {
        return false;
    }
    
    /**
     * 🔄 备选延长线计算（用于平行线情况）
     */
    private final java.util.List<kotlin.Pair<android.graphics.PointF, android.graphics.PointF>> calculateFallbackExtensionLines(java.util.List<? extends android.graphics.PointF> points) {
        return null;
    }
    
    /**
     * 🛡️ 检测触摸点是否在图像内容区域（避免UI按钮区域）
     */
    private final boolean isInImageContentArea(android.graphics.PointF touchPoint, int viewWidth, int viewHeight) {
        return false;
    }
    
    /**
     * 🎯 向量标准化辅助方法
     */
    private final android.graphics.PointF normalizeVector(float x, float y) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/touptek/measurerealize/utils/FourPointAngleHelper$Companion;", "", "()V", "CLICK_TOLERANCE", "", "LONG_PRESS_DURATION", "", "TAG", "", "TOUCH_RADIUS", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}