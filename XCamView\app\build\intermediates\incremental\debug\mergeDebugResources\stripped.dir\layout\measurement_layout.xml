<!-- 在 res/layout 目录下创建 popup_menu_layout.xml -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingVertical="0dp"
    android:paddingHorizontal="8dp">

    <ImageButton
        android:id="@+id/btn_calibration"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:background="@drawable/ic_calibration_n"
        android:contentDescription="@string/button_desc_calibration"
        android:layout_marginEnd="8dp"/>

    <!-- 所有按钮尺寸升级为64dp -->
    <ImageButton
        android:id="@+id/btn_delete_measurement"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:background="@drawable/ic_delete_n"
        android:contentDescription="@string/button_desc_delete_measurement"
        android:layout_marginEnd="8dp"/>

    <ImageButton
        android:id="@+id/btn_angle"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:background="@drawable/ic_angle_n"
        android:contentDescription="@string/button_desc_angle"
        android:layout_marginEnd="8dp"/>

    <ImageButton
        android:id="@+id/btn_angle_three"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:background="@drawable/ic_fourptangle_n"
        android:contentDescription="@string/button_desc_angle_three"
        android:layout_marginEnd="8dp"/>

    <ImageButton
        android:id="@+id/btn_point"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:background="@drawable/ic_point_n"
        android:contentDescription="@string/button_desc_point"
        android:layout_marginEnd="8dp"/>

    <ImageButton
        android:id="@+id/btn_arb_line"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:background="@drawable/ic_arbline_n"
        android:contentDescription="@string/button_desc_arb_line"
        android:layout_marginEnd="8dp"/>

    <ImageButton
        android:id="@+id/btn_three_line"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:background="@drawable/ic_threeline_n"
        android:contentDescription="@string/button_desc_three_line"
        android:layout_marginEnd="8dp"/>

    <ImageButton
        android:id="@+id/btn_horizon_line"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:background="@drawable/ic_hline_n"
        android:contentDescription="@string/button_desc_horizon_line"
        android:layout_marginEnd="8dp"/>

    <ImageButton
        android:id="@+id/btn_vertical_line"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:background="@drawable/ic_vline_n"
        android:contentDescription="@string/button_desc_vertical_line"
        android:layout_marginEnd="8dp"/>

    <ImageButton
        android:id="@+id/btn_parallel_line"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:background="@drawable/ic_parallel_n"
        android:contentDescription="@string/button_desc_parallel_line"
        android:layout_marginEnd="8dp"/>

    <ImageButton
        android:id="@+id/btn_three_vertical"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:background="@drawable/ic_threevertical_n"
        android:contentDescription="@string/button_desc_three_vertical"
        android:layout_marginEnd="8dp"/>

    <ImageButton
        android:id="@+id/btn_rectangle"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:background="@drawable/ic_rectangle_n"
        android:contentDescription="@string/button_desc_rectangle"
        android:layout_marginEnd="8dp"/>

    <ImageButton
        android:id="@+id/btn_three_rectangle"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:background="@drawable/ic_threerectangle_n"
        android:contentDescription="@string/button_desc_three_rectangle"
        android:layout_marginEnd="8dp"/>

    <ImageButton
        android:id="@+id/btn_ellipse"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:background="@drawable/ic_ellipse_n"
        android:contentDescription="@string/button_desc_ellipse"
        android:layout_marginEnd="8dp"/>

    <ImageButton
        android:id="@+id/btn_five_ellipse"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:background="@drawable/ic_fiveellipse_n"
        android:contentDescription="@string/button_desc_five_ellipse"
        android:layout_marginEnd="8dp"/>

    <ImageButton
        android:id="@+id/btn_center_circle"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:background="@drawable/ic_centerc_n"
        android:contentDescription="@string/button_desc_center_circle"
        android:layout_marginEnd="8dp"/>

    <ImageButton
        android:id="@+id/btn_three_circle"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:background="@drawable/ic_threecircle_n"
        android:contentDescription="@string/button_desc_three_circle"
        android:layout_marginEnd="8dp"/>

    <ImageButton
        android:id="@+id/btn_annulus"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:background="@drawable/ic_annulus_n"
        android:contentDescription="@string/button_desc_annulus"
        android:layout_marginEnd="8dp"/>

    <ImageButton
        android:id="@+id/btn_annulus2"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:background="@drawable/ic_annulus2_n"
        android:contentDescription="@string/button_desc_annulus2"
        android:layout_marginEnd="8dp"/>

    <ImageButton
        android:id="@+id/btn_twocircles"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:background="@drawable/ic_angle_n"
        android:contentDescription="@string/button_desc_twocircles"
        android:layout_marginEnd="8dp"/>

    <ImageButton
        android:id="@+id/btn_three_twocircles"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:background="@drawable/ic_threepttwowircles_n"
        android:contentDescription="@string/button_desc_three_twocircles"
        android:layout_marginEnd="8dp"/>

    <ImageButton
        android:id="@+id/btn_arc"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:background="@drawable/ic_arc_n"
        android:contentDescription="@string/button_desc_arc"
        android:layout_marginEnd="8dp"/>

</LinearLayout>