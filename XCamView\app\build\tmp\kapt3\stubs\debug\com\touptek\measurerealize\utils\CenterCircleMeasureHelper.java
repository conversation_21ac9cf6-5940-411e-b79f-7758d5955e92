package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 🎯 中心圆测量助手 - 专业级圆形测量系统
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000t\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010!\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0010\u000e\n\u0002\b\u0002\u0018\u0000 ;2\u00020\u0001:\u0001;B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0019\u001a\u00020\u0013J\u0006\u0010\u001a\u001a\u00020\u0013J\u0006\u0010\u001b\u001a\u00020\nJ\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00040\u001dJ\u0006\u0010\u001e\u001a\u00020\u0006J\b\u0010\u001f\u001a\u0004\u0018\u00010 J\b\u0010!\u001a\u0004\u0018\u00010\u0004J\u001e\u0010\"\u001a\u00020\n2\u0006\u0010#\u001a\u00020$2\u0006\u0010%\u001a\u00020\u00062\u0006\u0010&\u001a\u00020\u0006J\u0010\u0010\'\u001a\u00020\n2\u0006\u0010(\u001a\u00020)H\u0002J\u0010\u0010*\u001a\u00020\n2\u0006\u0010(\u001a\u00020)H\u0002J\u0006\u0010+\u001a\u00020\nJ\u0016\u0010,\u001a\u00020\u00132\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010-\u001a\u00020\u0017J\u0006\u0010\t\u001a\u00020\nJ \u0010.\u001a\u00020\n2\u0006\u0010(\u001a\u00020)2\u0006\u0010%\u001a\u00020\u00062\u0006\u0010&\u001a\u00020\u0006H\u0002J\u000e\u0010/\u001a\u00020\n2\u0006\u0010(\u001a\u00020)J\u000e\u00100\u001a\u00020\n2\u0006\u0010(\u001a\u00020)J\b\u00101\u001a\u00020\u0013H\u0002J\u0006\u00102\u001a\u00020\u0013J\u0006\u00103\u001a\u00020\u0013J\u0006\u00104\u001a\u00020\u0013J\u0010\u00105\u001a\u00020\u00132\u0006\u00106\u001a\u00020\u0004H\u0002J\u0014\u00107\u001a\u00020\u00132\f\u00108\u001a\b\u0012\u0004\u0012\u00020\u00130\u0012J\u0006\u00109\u001a\u00020:R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0011\u001a\n\u0012\u0004\u0012\u00020\u0013\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00040\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0018\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006<"}, d2 = {"Lcom/touptek/measurerealize/utils/CenterCircleMeasureHelper;", "", "()V", "activeMeasurement", "Lcom/touptek/measurerealize/utils/CenterCircleMeasurement;", "draggedPointIndex", "", "imageView", "Lcom/touptek/measurerealize/TpImageView;", "isDraggingPoint", "", "isInitialized", "lastTouchX", "", "lastTouchY", "longPressStartTime", "", "measurementUpdateCallback", "Lkotlin/Function0;", "", "measurements", "", "originalBitmap", "Landroid/graphics/Bitmap;", "selectedMeasurement", "clearAllMeasurements", "clearSelection", "deleteSelectedMeasurement", "getAllMeasurementData", "", "getMeasurementCount", "getMeasurementData", "Lcom/touptek/measurerealize/utils/CircleMeasurementData;", "getSelectedMeasurement", "handleTouchEvent", "event", "Landroid/view/MotionEvent;", "viewWidth", "viewHeight", "handleTouchMove", "touchPoint", "Landroid/graphics/PointF;", "handleTouchUp", "hasSelectedMeasurement", "init", "bitmap", "isInImageContentArea", "isNearAnyMeasurement", "isPointOnMeasurement", "notifyUpdate", "onScaleChanged", "pauseMeasurement", "resumeMeasurement", "selectMeasurement", "measurement", "setMeasurementUpdateCallback", "callback", "startNewMeasurement", "", "Companion", "app_debug"})
public final class CenterCircleMeasureHelper {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.measurerealize.utils.CenterCircleMeasureHelper.Companion Companion = null;
    private static final java.lang.String TAG = "CenterCircleMeasureHelper";
    private static final float TOUCH_RADIUS = 80.0F;
    private com.touptek.measurerealize.TpImageView imageView;
    private android.graphics.Bitmap originalBitmap;
    private boolean isInitialized = false;
    private final java.util.List<com.touptek.measurerealize.utils.CenterCircleMeasurement> measurements = null;
    private com.touptek.measurerealize.utils.CenterCircleMeasurement selectedMeasurement;
    private com.touptek.measurerealize.utils.CenterCircleMeasurement activeMeasurement;
    private boolean isDraggingPoint = false;
    private int draggedPointIndex = -1;
    private long longPressStartTime = 0L;
    private float lastTouchX = 0.0F;
    private float lastTouchY = 0.0F;
    private kotlin.jvm.functions.Function0<kotlin.Unit> measurementUpdateCallback;
    
    public CenterCircleMeasureHelper() {
        super();
    }
    
    /**
     * 🎯 初始化助手
     */
    public final void init(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.TpImageView imageView, @org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
    }
    
    /**
     * 🎯 设置测量更新回调
     */
    public final void setMeasurementUpdateCallback(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    /**
     * 🎯 开始新的圆形测量 - 在屏幕中央创建圆
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String startNewMeasurement() {
        return null;
    }
    
    /**
     * 🎯 获取所有测量数据用于覆盖层显示
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.touptek.measurerealize.utils.CenterCircleMeasurement> getAllMeasurementData() {
        return null;
    }
    
    /**
     * 🎯 获取当前选中的测量数据（向后兼容）
     */
    @org.jetbrains.annotations.Nullable
    public final com.touptek.measurerealize.utils.CircleMeasurementData getMeasurementData() {
        return null;
    }
    
    /**
     * 🎯 处理触摸事件 - 标准模式实现
     */
    public final boolean handleTouchEvent(@org.jetbrains.annotations.NotNull
    android.view.MotionEvent event, int viewWidth, int viewHeight) {
        return false;
    }
    
    private final boolean handleTouchMove(android.graphics.PointF touchPoint) {
        return false;
    }
    
    private final boolean handleTouchUp(android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🎯 选择测量
     */
    private final void selectMeasurement(com.touptek.measurerealize.utils.CenterCircleMeasurement measurement) {
    }
    
    /**
     * 🎯 通知更新
     */
    private final void notifyUpdate() {
    }
    
    /**
     * 🎯 暂停测量 - 保持数据但停止交互
     */
    public final void pauseMeasurement() {
    }
    
    /**
     * 🎯 恢复测量 - 从暂停状态恢复到可编辑状态
     */
    public final void resumeMeasurement() {
    }
    
    /**
     * 🎯 缩放变化时同步坐标
     */
    public final void onScaleChanged() {
    }
    
    /**
     * 🎯 获取测量数量
     */
    public final int getMeasurementCount() {
        return 0;
    }
    
    /**
     * 🎯 获取选中的测量
     */
    @org.jetbrains.annotations.Nullable
    public final com.touptek.measurerealize.utils.CenterCircleMeasurement getSelectedMeasurement() {
        return null;
    }
    
    /**
     * 🎯 删除选中的测量
     */
    public final boolean deleteSelectedMeasurement() {
        return false;
    }
    
    /**
     * 🎯 清除所有测量
     */
    public final void clearAllMeasurements() {
    }
    
    /**
     * 🎯 检查是否有选中的测量
     */
    public final boolean hasSelectedMeasurement() {
        return false;
    }
    
    /**
     * 🎯 检查是否正在拖拽点
     */
    public final boolean isDraggingPoint() {
        return false;
    }
    
    /**
     * ⭕ 清除所有选中状态
     */
    public final void clearSelection() {
    }
    
    /**
     * ⭕ 检查是否靠近任何测量
     */
    public final boolean isNearAnyMeasurement(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * ⭕ 检查点是否在测量上（用于混合触摸处理器）
     */
    public final boolean isPointOnMeasurement(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🎯 判断触摸点是否在图像内容区域（避免UI区域误触）
     */
    private final boolean isInImageContentArea(android.graphics.PointF touchPoint, int viewWidth, int viewHeight) {
        return false;
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0007\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/touptek/measurerealize/utils/CenterCircleMeasureHelper$Companion;", "", "()V", "TAG", "", "TOUCH_RADIUS", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}