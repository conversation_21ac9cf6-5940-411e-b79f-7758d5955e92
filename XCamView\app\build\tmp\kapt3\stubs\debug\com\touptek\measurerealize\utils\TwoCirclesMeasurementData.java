package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * ⭕⭕ 双圆测量数据 - 同心圆组合测量
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u001a\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B_\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\b\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0006\u0012\u0006\u0010\n\u001a\u00020\u0006\u0012\u0006\u0010\u000b\u001a\u00020\u0006\u0012\u0006\u0010\f\u001a\u00020\u0006\u0012\b\b\u0002\u0010\r\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u000e\u00a2\u0006\u0002\u0010\u0010J\u000f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u000eH\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0006H\u00c6\u0003J\t\u0010 \u001a\u00020\u0006H\u00c6\u0003J\t\u0010!\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\"\u001a\u00020\u0006H\u00c6\u0003J\t\u0010#\u001a\u00020\u0006H\u00c6\u0003J\t\u0010$\u001a\u00020\u0006H\u00c6\u0003J\t\u0010%\u001a\u00020\u000eH\u00c6\u0003Js\u0010&\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00062\b\b\u0002\u0010\b\u001a\u00020\u00062\b\b\u0002\u0010\t\u001a\u00020\u00062\b\b\u0002\u0010\n\u001a\u00020\u00062\b\b\u0002\u0010\u000b\u001a\u00020\u00062\b\b\u0002\u0010\f\u001a\u00020\u00062\b\b\u0002\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u000eH\u00c6\u0001J\u0013\u0010\'\u001a\u00020\u000e2\b\u0010(\u001a\u0004\u0018\u00010)H\u00d6\u0003J\t\u0010*\u001a\u00020+H\u00d6\u0001J\t\u0010,\u001a\u00020-H\u00d6\u0001R\u0011\u0010\b\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u000b\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0012R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0012R\u0011\u0010\r\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u0015R\u0011\u0010\u000f\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0015R\u0011\u0010\t\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0012R\u0011\u0010\f\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0012R\u0011\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0012R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0011\u0010\n\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0012\u00a8\u0006."}, d2 = {"Lcom/touptek/measurerealize/utils/TwoCirclesMeasurementData;", "Lcom/touptek/measurerealize/utils/MeasurementData;", "points", "", "Landroid/graphics/PointF;", "innerRadius", "", "outerRadius", "innerArea", "outerArea", "ringArea", "innerPerimeter", "outerPerimeter", "isDragging", "", "isSelected", "(Ljava/util/List;DDDDDDDZZ)V", "getInnerArea", "()D", "getInnerPerimeter", "getInnerRadius", "()Z", "getOuterArea", "getOuterPerimeter", "getOuterRadius", "getPoints", "()Ljava/util/List;", "getRingArea", "component1", "component10", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "", "hashCode", "", "toString", "", "app_debug"})
public final class TwoCirclesMeasurementData extends com.touptek.measurerealize.utils.MeasurementData {
    @org.jetbrains.annotations.NotNull
    private final java.util.List<android.graphics.PointF> points = null;
    private final double innerRadius = 0.0;
    private final double outerRadius = 0.0;
    private final double innerArea = 0.0;
    private final double outerArea = 0.0;
    private final double ringArea = 0.0;
    private final double innerPerimeter = 0.0;
    private final double outerPerimeter = 0.0;
    private final boolean isDragging = false;
    private final boolean isSelected = false;
    
    /**
     * ⭕⭕ 双圆测量数据 - 同心圆组合测量
     */
    @org.jetbrains.annotations.NotNull
    public final com.touptek.measurerealize.utils.TwoCirclesMeasurementData copy(@org.jetbrains.annotations.NotNull
    java.util.List<? extends android.graphics.PointF> points, double innerRadius, double outerRadius, double innerArea, double outerArea, double ringArea, double innerPerimeter, double outerPerimeter, boolean isDragging, boolean isSelected) {
        return null;
    }
    
    /**
     * ⭕⭕ 双圆测量数据 - 同心圆组合测量
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * ⭕⭕ 双圆测量数据 - 同心圆组合测量
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * ⭕⭕ 双圆测量数据 - 同心圆组合测量
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public TwoCirclesMeasurementData(@org.jetbrains.annotations.NotNull
    java.util.List<? extends android.graphics.PointF> points, double innerRadius, double outerRadius, double innerArea, double outerArea, double ringArea, double innerPerimeter, double outerPerimeter, boolean isDragging, boolean isSelected) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> getPoints() {
        return null;
    }
    
    public final double component2() {
        return 0.0;
    }
    
    public final double getInnerRadius() {
        return 0.0;
    }
    
    public final double component3() {
        return 0.0;
    }
    
    public final double getOuterRadius() {
        return 0.0;
    }
    
    public final double component4() {
        return 0.0;
    }
    
    public final double getInnerArea() {
        return 0.0;
    }
    
    public final double component5() {
        return 0.0;
    }
    
    public final double getOuterArea() {
        return 0.0;
    }
    
    public final double component6() {
        return 0.0;
    }
    
    public final double getRingArea() {
        return 0.0;
    }
    
    public final double component7() {
        return 0.0;
    }
    
    public final double getInnerPerimeter() {
        return 0.0;
    }
    
    public final double component8() {
        return 0.0;
    }
    
    public final double getOuterPerimeter() {
        return 0.0;
    }
    
    public final boolean component9() {
        return false;
    }
    
    public final boolean isDragging() {
        return false;
    }
    
    public final boolean component10() {
        return false;
    }
    
    public final boolean isSelected() {
        return false;
    }
}