<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="videodecode_layout" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\videodecode_layout.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/videodecode_layout_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="74" endOffset="14"/></Target><Target id="@+id/surface_view" view="TextureView"><Expressions/><location startLine="7" startOffset="4" endLine="11" endOffset="35"/></Target><Target id="@+id/seek_bar" view="SeekBar"><Expressions/><location startLine="13" startOffset="4" endLine="17" endOffset="41"/></Target><Target id="@+id/tv_duration" view="TextView"><Expressions/><location startLine="25" startOffset="8" endLine="29" endOffset="42"/></Target><Target id="@+id/tv_current_position" view="TextView"><Expressions/><location startLine="31" startOffset="8" endLine="35" endOffset="43"/></Target><Target id="@+id/btn_play_pause" view="Button"><Expressions/><location startLine="44" startOffset="8" endLine="48" endOffset="31"/></Target><Target id="@+id/btn_fast_forward" view="Button"><Expressions/><location startLine="50" startOffset="8" endLine="54" endOffset="31"/></Target><Target id="@+id/btn_fast_backward" view="Button"><Expressions/><location startLine="56" startOffset="8" endLine="60" endOffset="31"/></Target><Target id="@+id/btn_step_decode" view="Button"><Expressions/><location startLine="62" startOffset="8" endLine="66" endOffset="33"/></Target><Target id="@+id/btn_decode_return" view="Button"><Expressions/><location startLine="68" startOffset="8" endLine="72" endOffset="31"/></Target></Targets></Layout>