<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="testdialog_settings" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\testdialog_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout/testdialog_settings_0" view="FrameLayout"><Expressions/><location startLine="0" startOffset="0" endLine="225" endOffset="13"/></Target><Target id="@+id/network_tab_container" view="LinearLayout"><Expressions/><location startLine="20" startOffset="12" endLine="49" endOffset="26"/></Target><Target id="@+id/network_icon" view="ImageView"><Expressions/><location startLine="32" startOffset="16" endLine="38" endOffset="52"/></Target><Target id="@+id/item_network" view="TextView"><Expressions/><location startLine="40" startOffset="16" endLine="48" endOffset="45"/></Target><Target id="@+id/storage_tab_container" view="LinearLayout"><Expressions/><location startLine="51" startOffset="12" endLine="80" endOffset="26"/></Target><Target id="@+id/storage_icon" view="ImageView"><Expressions/><location startLine="63" startOffset="16" endLine="69" endOffset="52"/></Target><Target id="@+id/item_storage" view="TextView"><Expressions/><location startLine="71" startOffset="16" endLine="79" endOffset="45"/></Target><Target id="@+id/format_tab_container" view="LinearLayout"><Expressions/><location startLine="82" startOffset="12" endLine="113" endOffset="26"/></Target><Target id="@+id/format_icon" view="ImageView"><Expressions/><location startLine="94" startOffset="16" endLine="100" endOffset="52"/></Target><Target id="@+id/item_format" view="TextView"><Expressions/><location startLine="102" startOffset="16" endLine="112" endOffset="45"/></Target><Target id="@+id/video_tab_container" view="LinearLayout"><Expressions/><location startLine="116" startOffset="12" endLine="145" endOffset="26"/></Target><Target id="@+id/video_icon" view="ImageView"><Expressions/><location startLine="128" startOffset="16" endLine="134" endOffset="56"/></Target><Target id="@+id/item_video" view="TextView"><Expressions/><location startLine="136" startOffset="16" endLine="144" endOffset="45"/></Target><Target id="@+id/measurement_tab_container" view="LinearLayout"><Expressions/><location startLine="147" startOffset="12" endLine="176" endOffset="26"/></Target><Target id="@+id/measurement_icon" view="ImageView"><Expressions/><location startLine="159" startOffset="16" endLine="165" endOffset="52"/></Target><Target id="@+id/item_measurement" view="TextView"><Expressions/><location startLine="167" startOffset="16" endLine="175" endOffset="45"/></Target><Target id="@+id/misc_tab_container" view="LinearLayout"><Expressions/><location startLine="179" startOffset="12" endLine="206" endOffset="26"/></Target><Target id="@+id/about_icon" view="ImageView"><Expressions/><location startLine="191" startOffset="16" endLine="197" endOffset="52"/></Target><Target id="@+id/item_misc" view="TextView"><Expressions/><location startLine="199" startOffset="16" endLine="205" endOffset="45"/></Target><Target id="@+id/content_container" view="FrameLayout"><Expressions/><location startLine="210" startOffset="8" endLine="214" endOffset="57"/></Target><Target id="@+id/btn_close" view="ImageButton"><Expressions/><location startLine="217" startOffset="4" endLine="224" endOffset="55"/></Target></Targets></Layout>