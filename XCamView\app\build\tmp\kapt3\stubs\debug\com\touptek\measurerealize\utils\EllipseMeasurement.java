package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 🔵 椭圆测量实例 - 专业级数据结构
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b.\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\u0007\n\u0002\b\f\b\u0086\b\u0018\u00002\u00020\u0001B\u008f\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\t\u0012\b\b\u0002\u0010\u000b\u001a\u00020\t\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0006\u0012\b\b\u0002\u0010\r\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u000e\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0011\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0011\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u000e\u00a2\u0006\u0002\u0010\u0014J\u0006\u0010-\u001a\u00020\u0011J\u0006\u0010.\u001a\u00020\u0011J\u0006\u0010/\u001a\u00020\u0006J\t\u00100\u001a\u00020\u0003H\u00c6\u0003J\u0010\u00101\u001a\u0004\u0018\u00010\u0011H\u00c2\u0003\u00a2\u0006\u0002\u00102J\u0010\u00103\u001a\u0004\u0018\u00010\u0011H\u00c2\u0003\u00a2\u0006\u0002\u00102J\t\u00104\u001a\u00020\u000eH\u00c2\u0003J\u000f\u00105\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\u000f\u00106\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\t\u00107\u001a\u00020\tH\u00c6\u0003J\t\u00108\u001a\u00020\tH\u00c6\u0003J\t\u00109\u001a\u00020\tH\u00c6\u0003J\u000b\u0010:\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\t\u0010;\u001a\u00020\u000eH\u00c6\u0003J\t\u0010<\u001a\u00020\u000eH\u00c6\u0003J\b\u0010=\u001a\u00020\u0011H\u0002J\b\u0010>\u001a\u00020\u0011H\u0002J\b\u0010?\u001a\u00020@H\u0002J\u0018\u0010A\u001a\u00020\u00062\u0006\u0010B\u001a\u00020\u00062\u0006\u0010C\u001a\u00020DH\u0002J\u0018\u0010E\u001a\u00020\u00062\u0006\u0010F\u001a\u00020\u00062\u0006\u0010C\u001a\u00020DH\u0002J\u0098\u0001\u0010G\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\t2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u00062\b\b\u0002\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u000e2\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00112\b\b\u0002\u0010\u0013\u001a\u00020\u000eH\u00c6\u0001\u00a2\u0006\u0002\u0010HJ\u0013\u0010I\u001a\u00020\t2\b\u0010J\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u000e\u0010K\u001a\u00020L2\u0006\u0010M\u001a\u00020\u0006J\t\u0010N\u001a\u00020LH\u00d6\u0001J\b\u0010O\u001a\u00020@H\u0002J\u001e\u0010P\u001a\u00020\t2\u0006\u0010M\u001a\u00020\u00062\u0006\u0010Q\u001a\u00020L2\u0006\u0010R\u001a\u00020SJ\u000e\u0010T\u001a\u00020@2\u0006\u0010U\u001a\u00020\u0006J\u0010\u0010V\u001a\u00020\u00062\u0006\u0010W\u001a\u00020\u0006H\u0002J\u000e\u0010X\u001a\u00020@2\u0006\u0010C\u001a\u00020DJ\u000e\u0010Y\u001a\u00020@2\u0006\u0010C\u001a\u00020DJ\t\u0010Z\u001a\u00020\u0003H\u00d6\u0001J\u000e\u0010[\u001a\u00020@2\u0006\u0010\\\u001a\u00020\u0006J\u000e\u0010]\u001a\u00020@2\u0006\u0010\\\u001a\u00020\u0006J\u0006\u0010^\u001a\u00020\tR \u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0015\u0010\u0016\"\u0004\b\u0017\u0010\u0018R\u000e\u0010\u0013\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0012\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010\u0019R\u0012\u0010\u0012\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010\u0019R\u001a\u0010\r\u001a\u00020\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001a\u0010\u001b\"\u0004\b\u001c\u0010\u001dR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001fR\u001a\u0010\u000b\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000b\u0010 \"\u0004\b!\u0010\"R\u001a\u0010\n\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\n\u0010 \"\u0004\b#\u0010\"R\u001a\u0010\b\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\b\u0010 \"\u0004\b$\u0010\"R\u001a\u0010\u000f\u001a\u00020\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b%\u0010\u001b\"\u0004\b&\u0010\u001dR\u001c\u0010\f\u001a\u0004\u0018\u00010\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\'\u0010(\"\u0004\b)\u0010*R \u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b+\u0010\u0016\"\u0004\b,\u0010\u0018\u00a8\u0006_"}, d2 = {"Lcom/touptek/measurerealize/utils/EllipseMeasurement;", "", "id", "", "viewPoints", "", "Landroid/graphics/PointF;", "bitmapPoints", "isSelected", "", "isEditing", "isCompleted", "textPosition", "creationTime", "", "lastModified", "cachedArea", "", "cachedPerimeter", "cacheValidTime", "(Ljava/lang/String;Ljava/util/List;Ljava/util/List;ZZZLandroid/graphics/PointF;JJLjava/lang/Double;Ljava/lang/Double;J)V", "getBitmapPoints", "()Ljava/util/List;", "setBitmapPoints", "(Ljava/util/List;)V", "Ljava/lang/Double;", "getCreationTime", "()J", "setCreationTime", "(J)V", "getId", "()Ljava/lang/String;", "()Z", "setCompleted", "(Z)V", "setEditing", "setSelected", "getLastModified", "setLastModified", "getTextPosition", "()Landroid/graphics/PointF;", "setTextPosition", "(Landroid/graphics/PointF;)V", "getViewPoints", "setViewPoints", "calculateArea", "calculatePerimeter", "calculateTextPosition", "component1", "component10", "()Ljava/lang/Double;", "component11", "component12", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "computeArea", "computePerimeter", "constrainMinorAxisToPerpendicularDirection", "", "convertBitmapToViewCoords", "bitmapPoint", "imageView", "Lcom/touptek/measurerealize/TpImageView;", "convertViewToBitmapCoords", "viewPoint", "copy", "(Ljava/lang/String;Ljava/util/List;Ljava/util/List;ZZZLandroid/graphics/PointF;JJLjava/lang/Double;Ljava/lang/Double;J)Lcom/touptek/measurerealize/utils/EllipseMeasurement;", "equals", "other", "getNearestControlPointIndex", "", "touchPoint", "hashCode", "invalidateCache", "isPointInTouchRange", "pointIndex", "touchRadius", "", "moveEntireEllipse", "newCenter", "projectToPerpendicularLine", "point", "syncBitmapCoords", "syncViewCoords", "toString", "updateMajorAxisEnd", "newPoint", "updateMinorAxisEnd", "validateEllipseGeometry", "app_debug"})
public final class EllipseMeasurement {
    @org.jetbrains.annotations.NotNull
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull
    private java.util.List<android.graphics.PointF> viewPoints;
    @org.jetbrains.annotations.NotNull
    private java.util.List<android.graphics.PointF> bitmapPoints;
    private boolean isSelected;
    private boolean isEditing;
    private boolean isCompleted;
    @org.jetbrains.annotations.Nullable
    private android.graphics.PointF textPosition;
    private long creationTime;
    private long lastModified;
    private java.lang.Double cachedArea;
    private java.lang.Double cachedPerimeter;
    private long cacheValidTime;
    
    /**
     * 🔵 椭圆测量实例 - 专业级数据结构
     */
    @org.jetbrains.annotations.NotNull
    public final com.touptek.measurerealize.utils.EllipseMeasurement copy(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> viewPoints, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> bitmapPoints, boolean isSelected, boolean isEditing, boolean isCompleted, @org.jetbrains.annotations.Nullable
    android.graphics.PointF textPosition, long creationTime, long lastModified, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedArea, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedPerimeter, long cacheValidTime) {
        return null;
    }
    
    /**
     * 🔵 椭圆测量实例 - 专业级数据结构
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 🔵 椭圆测量实例 - 专业级数据结构
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 🔵 椭圆测量实例 - 专业级数据结构
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public EllipseMeasurement() {
        super();
    }
    
    public EllipseMeasurement(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> viewPoints, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> bitmapPoints, boolean isSelected, boolean isEditing, boolean isCompleted, @org.jetbrains.annotations.Nullable
    android.graphics.PointF textPosition, long creationTime, long lastModified, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedArea, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedPerimeter, long cacheValidTime) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> getViewPoints() {
        return null;
    }
    
    public final void setViewPoints(@org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> p0) {
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> getBitmapPoints() {
        return null;
    }
    
    public final void setBitmapPoints(@org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> p0) {
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean isSelected() {
        return false;
    }
    
    public final void setSelected(boolean p0) {
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final boolean isEditing() {
        return false;
    }
    
    public final void setEditing(boolean p0) {
    }
    
    public final boolean component6() {
        return false;
    }
    
    public final boolean isCompleted() {
        return false;
    }
    
    public final void setCompleted(boolean p0) {
    }
    
    @org.jetbrains.annotations.Nullable
    public final android.graphics.PointF component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final android.graphics.PointF getTextPosition() {
        return null;
    }
    
    public final void setTextPosition(@org.jetbrains.annotations.Nullable
    android.graphics.PointF p0) {
    }
    
    public final long component8() {
        return 0L;
    }
    
    public final long getCreationTime() {
        return 0L;
    }
    
    public final void setCreationTime(long p0) {
    }
    
    public final long component9() {
        return 0L;
    }
    
    public final long getLastModified() {
        return 0L;
    }
    
    public final void setLastModified(long p0) {
    }
    
    private final java.lang.Double component10() {
        return null;
    }
    
    private final java.lang.Double component11() {
        return null;
    }
    
    private final long component12() {
        return 0L;
    }
    
    /**
     * 🔵 计算椭圆面积（使用位图坐标 - 真实面积，不受缩放影响）
     */
    public final double calculateArea() {
        return 0.0;
    }
    
    private final double computeArea() {
        return 0.0;
    }
    
    /**
     * 🔵 计算椭圆周长（使用位图坐标 - 真实周长，不受缩放影响）
     * 使用Ramanujan近似公式，精度高达99.5%
     */
    public final double calculatePerimeter() {
        return 0.0;
    }
    
    private final double computePerimeter() {
        return 0.0;
    }
    
    /**
     * 🔵 更新长轴端点（自由移动）
     */
    public final void updateMajorAxisEnd(@org.jetbrains.annotations.NotNull
    android.graphics.PointF newPoint) {
    }
    
    /**
     * 🔵 更新短轴端点（垂直约束）
     */
    public final void updateMinorAxisEnd(@org.jetbrains.annotations.NotNull
    android.graphics.PointF newPoint) {
    }
    
    /**
     * 🔵 将点投影到长轴的垂直线上
     */
    private final android.graphics.PointF projectToPerpendicularLine(android.graphics.PointF point) {
        return null;
    }
    
    /**
     * 🔵 约束短轴端点到垂直方向
     */
    private final void constrainMinorAxisToPerpendicularDirection() {
    }
    
    /**
     * 🔵 移动整个椭圆（拖动圆心时使用）
     */
    public final void moveEntireEllipse(@org.jetbrains.annotations.NotNull
    android.graphics.PointF newCenter) {
    }
    
    /**
     * 🔵 计算文本显示位置（圆心下方固定偏移）
     */
    @org.jetbrains.annotations.NotNull
    public final android.graphics.PointF calculateTextPosition() {
        return null;
    }
    
    /**
     * 🔵 清除计算缓存
     */
    private final void invalidateCache() {
    }
    
    /**
     * 🔵 检查点是否在触摸范围内
     */
    public final boolean isPointInTouchRange(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint, int pointIndex, float touchRadius) {
        return false;
    }
    
    /**
     * 🔵 获取最近的控制点索引
     */
    public final int getNearestControlPointIndex(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return 0;
    }
    
    /**
     * 🔄 同步位图坐标 - 从视图坐标转换到位图坐标
     */
    public final void syncBitmapCoords(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.TpImageView imageView) {
    }
    
    /**
     * 🔄 同步视图坐标 - 从位图坐标转换到视图坐标
     */
    public final void syncViewCoords(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.TpImageView imageView) {
    }
    
    /**
     * 🔄 视图坐标到位图坐标转换
     */
    private final android.graphics.PointF convertViewToBitmapCoords(android.graphics.PointF viewPoint, com.touptek.measurerealize.TpImageView imageView) {
        return null;
    }
    
    /**
     * 🔄 位图坐标到视图坐标转换
     */
    private final android.graphics.PointF convertBitmapToViewCoords(android.graphics.PointF bitmapPoint, com.touptek.measurerealize.TpImageView imageView) {
        return null;
    }
    
    /**
     * 🔵 验证椭圆几何有效性
     */
    public final boolean validateEllipseGeometry() {
        return false;
    }
}