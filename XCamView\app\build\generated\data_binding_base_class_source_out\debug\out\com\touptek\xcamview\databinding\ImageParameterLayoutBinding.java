// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ImageParameterLayoutBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton btnBrightnessAdd;

  @NonNull
  public final ImageButton btnBrightnessReduce;

  @NonNull
  public final ImageButton btnContrastAdd;

  @NonNull
  public final ImageButton btnContrastReduce;

  @NonNull
  public final Button btnDefaultImageParameter;

  @NonNull
  public final ImageButton btnGammaAdd;

  @NonNull
  public final ImageButton btnGammaReduce;

  @NonNull
  public final ImageButton btnSaturationAdd;

  @NonNull
  public final ImageButton btnSaturationReduce;

  @NonNull
  public final SeekBar seekbarBrightnessTv;

  @NonNull
  public final SeekBar seekbarContrastTv;

  @NonNull
  public final SeekBar seekbarGammaTv;

  @NonNull
  public final SeekBar seekbarSaturationTv;

  @NonNull
  public final TextView textBrightnessValue;

  @NonNull
  public final TextView textContrastValue;

  @NonNull
  public final TextView textGammaValue;

  @NonNull
  public final TextView textSaturationValue;

  private ImageParameterLayoutBinding(@NonNull LinearLayout rootView,
      @NonNull ImageButton btnBrightnessAdd, @NonNull ImageButton btnBrightnessReduce,
      @NonNull ImageButton btnContrastAdd, @NonNull ImageButton btnContrastReduce,
      @NonNull Button btnDefaultImageParameter, @NonNull ImageButton btnGammaAdd,
      @NonNull ImageButton btnGammaReduce, @NonNull ImageButton btnSaturationAdd,
      @NonNull ImageButton btnSaturationReduce, @NonNull SeekBar seekbarBrightnessTv,
      @NonNull SeekBar seekbarContrastTv, @NonNull SeekBar seekbarGammaTv,
      @NonNull SeekBar seekbarSaturationTv, @NonNull TextView textBrightnessValue,
      @NonNull TextView textContrastValue, @NonNull TextView textGammaValue,
      @NonNull TextView textSaturationValue) {
    this.rootView = rootView;
    this.btnBrightnessAdd = btnBrightnessAdd;
    this.btnBrightnessReduce = btnBrightnessReduce;
    this.btnContrastAdd = btnContrastAdd;
    this.btnContrastReduce = btnContrastReduce;
    this.btnDefaultImageParameter = btnDefaultImageParameter;
    this.btnGammaAdd = btnGammaAdd;
    this.btnGammaReduce = btnGammaReduce;
    this.btnSaturationAdd = btnSaturationAdd;
    this.btnSaturationReduce = btnSaturationReduce;
    this.seekbarBrightnessTv = seekbarBrightnessTv;
    this.seekbarContrastTv = seekbarContrastTv;
    this.seekbarGammaTv = seekbarGammaTv;
    this.seekbarSaturationTv = seekbarSaturationTv;
    this.textBrightnessValue = textBrightnessValue;
    this.textContrastValue = textContrastValue;
    this.textGammaValue = textGammaValue;
    this.textSaturationValue = textSaturationValue;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ImageParameterLayoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ImageParameterLayoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.image_parameter_layout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ImageParameterLayoutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_brightness_add;
      ImageButton btnBrightnessAdd = ViewBindings.findChildViewById(rootView, id);
      if (btnBrightnessAdd == null) {
        break missingId;
      }

      id = R.id.btn_brightness_reduce;
      ImageButton btnBrightnessReduce = ViewBindings.findChildViewById(rootView, id);
      if (btnBrightnessReduce == null) {
        break missingId;
      }

      id = R.id.btn_contrast_add;
      ImageButton btnContrastAdd = ViewBindings.findChildViewById(rootView, id);
      if (btnContrastAdd == null) {
        break missingId;
      }

      id = R.id.btn_contrast_reduce;
      ImageButton btnContrastReduce = ViewBindings.findChildViewById(rootView, id);
      if (btnContrastReduce == null) {
        break missingId;
      }

      id = R.id.btn_Default_image_parameter;
      Button btnDefaultImageParameter = ViewBindings.findChildViewById(rootView, id);
      if (btnDefaultImageParameter == null) {
        break missingId;
      }

      id = R.id.btn_gamma_add;
      ImageButton btnGammaAdd = ViewBindings.findChildViewById(rootView, id);
      if (btnGammaAdd == null) {
        break missingId;
      }

      id = R.id.btn_gamma_reduce;
      ImageButton btnGammaReduce = ViewBindings.findChildViewById(rootView, id);
      if (btnGammaReduce == null) {
        break missingId;
      }

      id = R.id.btn_saturation_add;
      ImageButton btnSaturationAdd = ViewBindings.findChildViewById(rootView, id);
      if (btnSaturationAdd == null) {
        break missingId;
      }

      id = R.id.btn_saturation_reduce;
      ImageButton btnSaturationReduce = ViewBindings.findChildViewById(rootView, id);
      if (btnSaturationReduce == null) {
        break missingId;
      }

      id = R.id.seekbar_brightness_tv;
      SeekBar seekbarBrightnessTv = ViewBindings.findChildViewById(rootView, id);
      if (seekbarBrightnessTv == null) {
        break missingId;
      }

      id = R.id.seekbar_contrast_tv;
      SeekBar seekbarContrastTv = ViewBindings.findChildViewById(rootView, id);
      if (seekbarContrastTv == null) {
        break missingId;
      }

      id = R.id.seekbar_gamma_tv;
      SeekBar seekbarGammaTv = ViewBindings.findChildViewById(rootView, id);
      if (seekbarGammaTv == null) {
        break missingId;
      }

      id = R.id.seekbar_saturation_tv;
      SeekBar seekbarSaturationTv = ViewBindings.findChildViewById(rootView, id);
      if (seekbarSaturationTv == null) {
        break missingId;
      }

      id = R.id.text_brightness_value;
      TextView textBrightnessValue = ViewBindings.findChildViewById(rootView, id);
      if (textBrightnessValue == null) {
        break missingId;
      }

      id = R.id.text_contrast_value;
      TextView textContrastValue = ViewBindings.findChildViewById(rootView, id);
      if (textContrastValue == null) {
        break missingId;
      }

      id = R.id.text_gamma_value;
      TextView textGammaValue = ViewBindings.findChildViewById(rootView, id);
      if (textGammaValue == null) {
        break missingId;
      }

      id = R.id.text_saturation_value;
      TextView textSaturationValue = ViewBindings.findChildViewById(rootView, id);
      if (textSaturationValue == null) {
        break missingId;
      }

      return new ImageParameterLayoutBinding((LinearLayout) rootView, btnBrightnessAdd,
          btnBrightnessReduce, btnContrastAdd, btnContrastReduce, btnDefaultImageParameter,
          btnGammaAdd, btnGammaReduce, btnSaturationAdd, btnSaturationReduce, seekbarBrightnessTv,
          seekbarContrastTv, seekbarGammaTv, seekbarSaturationTv, textBrightnessValue,
          textContrastValue, textGammaValue, textSaturationValue);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
