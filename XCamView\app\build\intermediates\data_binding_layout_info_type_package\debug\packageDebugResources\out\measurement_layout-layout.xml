<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="measurement_layout" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\measurement_layout.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/measurement_layout_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="185" endOffset="14"/></Target><Target id="@+id/btn_calibration" view="ImageButton"><Expressions/><location startLine="8" startOffset="4" endLine="14" endOffset="39"/></Target><Target id="@+id/btn_delete_measurement" view="ImageButton"><Expressions/><location startLine="17" startOffset="4" endLine="23" endOffset="39"/></Target><Target id="@+id/btn_angle" view="ImageButton"><Expressions/><location startLine="25" startOffset="4" endLine="31" endOffset="39"/></Target><Target id="@+id/btn_angle_three" view="ImageButton"><Expressions/><location startLine="33" startOffset="4" endLine="39" endOffset="39"/></Target><Target id="@+id/btn_point" view="ImageButton"><Expressions/><location startLine="41" startOffset="4" endLine="47" endOffset="39"/></Target><Target id="@+id/btn_arb_line" view="ImageButton"><Expressions/><location startLine="49" startOffset="4" endLine="55" endOffset="39"/></Target><Target id="@+id/btn_three_line" view="ImageButton"><Expressions/><location startLine="57" startOffset="4" endLine="63" endOffset="39"/></Target><Target id="@+id/btn_horizon_line" view="ImageButton"><Expressions/><location startLine="65" startOffset="4" endLine="71" endOffset="39"/></Target><Target id="@+id/btn_vertical_line" view="ImageButton"><Expressions/><location startLine="73" startOffset="4" endLine="79" endOffset="39"/></Target><Target id="@+id/btn_parallel_line" view="ImageButton"><Expressions/><location startLine="81" startOffset="4" endLine="87" endOffset="39"/></Target><Target id="@+id/btn_three_vertical" view="ImageButton"><Expressions/><location startLine="89" startOffset="4" endLine="95" endOffset="39"/></Target><Target id="@+id/btn_rectangle" view="ImageButton"><Expressions/><location startLine="97" startOffset="4" endLine="103" endOffset="39"/></Target><Target id="@+id/btn_three_rectangle" view="ImageButton"><Expressions/><location startLine="105" startOffset="4" endLine="111" endOffset="39"/></Target><Target id="@+id/btn_ellipse" view="ImageButton"><Expressions/><location startLine="113" startOffset="4" endLine="119" endOffset="39"/></Target><Target id="@+id/btn_five_ellipse" view="ImageButton"><Expressions/><location startLine="121" startOffset="4" endLine="127" endOffset="39"/></Target><Target id="@+id/btn_center_circle" view="ImageButton"><Expressions/><location startLine="129" startOffset="4" endLine="135" endOffset="39"/></Target><Target id="@+id/btn_three_circle" view="ImageButton"><Expressions/><location startLine="137" startOffset="4" endLine="143" endOffset="39"/></Target><Target id="@+id/btn_annulus" view="ImageButton"><Expressions/><location startLine="145" startOffset="4" endLine="151" endOffset="39"/></Target><Target id="@+id/btn_annulus2" view="ImageButton"><Expressions/><location startLine="153" startOffset="4" endLine="159" endOffset="39"/></Target><Target id="@+id/btn_twocircles" view="ImageButton"><Expressions/><location startLine="161" startOffset="4" endLine="167" endOffset="39"/></Target><Target id="@+id/btn_three_twocircles" view="ImageButton"><Expressions/><location startLine="169" startOffset="4" endLine="175" endOffset="39"/></Target><Target id="@+id/btn_arc" view="ImageButton"><Expressions/><location startLine="177" startOffset="4" endLine="183" endOffset="39"/></Target></Targets></Layout>