1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.touptek.xcamview"
4    android:sharedUserId="android.uid.system"
5    android:versionCode="5002"
6    android:versionName="14" >
7
8    <uses-sdk
9        android:minSdkVersion="31"
10        android:targetSdkVersion="34" />
11
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:7:5-81
12-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:7:22-78
13    <uses-permission android:name="android.permission.RECORD_AUDIO" />
13-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:8:5-71
13-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:8:22-68
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:9:5-80
14-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:9:22-77
15    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" /> <!-- 适用于Android 11及以上 -->
15-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:10:5-82
15-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:10:22-79
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:11:5-66
16-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:11:22-63
17
18    <permission
18-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
19        android:name="com.touptek.xcamview.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="com.touptek.xcamview.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
22-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
23
24    <application
24-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:13:5-48:19
25        android:allowBackup="true"
25-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:14:9-35
26        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
26-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
27        android:dataExtractionRules="@xml/data_extraction_rules"
27-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:15:9-65
28        android:debuggable="true"
29        android:extractNativeLibs="false"
30        android:fullBackupContent="@xml/backup_rules"
30-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:16:9-54
31        android:icon="@mipmap/ic_launcher"
31-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:17:9-43
32        android:label="XCamView"
32-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:18:9-33
33        android:roundIcon="@mipmap/ic_launcher_round"
33-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:19:9-54
34        android:supportsRtl="true"
34-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:20:9-35
35        android:testOnly="true"
36        android:theme="@style/Theme.AppCompat.DayNight.NoActionBar" >
36-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:21:9-68
37
38        <!-- android:theme="@style/Theme.AppCompat.DayNight.NoActionBar" -->
39        <!-- android:theme="@style/Theme.AppCompat.DayNight" -->
40
41
42        <!-- 设置MainActivity为启动Activity -->
43        <activity
43-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:29:9-42:20
44            android:name="com.touptek.xcamview.activity.MainActivity"
44-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:30:13-70
45            android:configChanges="keyboard|keyboardHidden|screenSize|smallestScreenSize|screenLayout|orientation"
45-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:31:13-115
46            android:exported="true"
46-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:32:13-36
47            android:launchMode="singleTask"
47-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:33:13-44
48            android:resizeableActivity="true"
48-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:34:13-46
49            android:screenOrientation="unspecified"
49-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:35:13-52
50            android:supportsPictureInPicture="true" >
50-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:36:13-52
51            <intent-filter>
51-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:37:13-41:29
52                <action android:name="android.intent.action.MAIN" />
52-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:38:17-69
52-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:38:25-66
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:39:17-77
54-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:39:27-74
55                <category android:name="android.intent.category.DEFAULT" />
55-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:40:17-76
55-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:40:27-73
56            </intent-filter>
57        </activity>
58        <activity android:name="com.touptek.xcamview.activity.videomanagement.TpVideoEncoderActivity" />
58-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:44:9-85
58-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:44:19-82
59        <activity android:name="com.touptek.xcamview.activity.videomanagement.TpVideoDecoderActivity" />
59-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:45:9-85
59-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:45:19-82
60        <activity android:name="com.touptek.xcamview.activity.browse.TpVideoBrowse" />
60-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:46:9-67
60-->E:\work\Andriod\XcamView_all\XCamView\app\src\main\AndroidManifest.xml:46:19-64
61
62        <provider
62-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
63            android:name="androidx.startup.InitializationProvider"
63-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
64            android:authorities="com.touptek.xcamview.androidx-startup"
64-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
65            android:exported="false" >
65-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
66            <meta-data
66-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
67                android:name="androidx.emoji2.text.EmojiCompatInitializer"
67-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
68                android:value="androidx.startup" />
68-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
69            <meta-data
69-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2659dca782c3902a5a89c540359c7180\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
70                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
70-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2659dca782c3902a5a89c540359c7180\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
71                android:value="androidx.startup" />
71-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2659dca782c3902a5a89c540359c7180\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
72            <meta-data
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
73                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
74                android:value="androidx.startup" />
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
75        </provider>
76
77        <receiver
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
78            android:name="androidx.profileinstaller.ProfileInstallReceiver"
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
79            android:directBootAware="false"
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
80            android:enabled="true"
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
81            android:exported="true"
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
82            android:permission="android.permission.DUMP" >
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
84                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
85            </intent-filter>
86            <intent-filter>
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
87                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
87-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
87-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
88            </intent-filter>
89            <intent-filter>
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
90                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
91            </intent-filter>
92            <intent-filter>
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
93                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
93-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
93-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
94            </intent-filter>
95        </receiver>
96    </application>
97
98</manifest>
