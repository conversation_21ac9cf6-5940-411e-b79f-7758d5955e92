package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 🎯 三点矩形测量实例 - 支持旋转的专业级数据结构
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0010!\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b(\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0012\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\u0002\n\u0002\b\f\b\u0086\b\u0018\u00002\u00020\u0001B\u00a3\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\u0007\u0012\b\b\u0002\u0010\t\u001a\u00020\u0007\u0012\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00050\u000b\u0012\b\b\u0002\u0010\f\u001a\u00020\u0005\u0012\b\b\u0002\u0010\r\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0010\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0010\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0010\u0012\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0015\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u0015\u00a2\u0006\u0002\u0010\u0017J\u0006\u0010=\u001a\u00020>J\u0006\u0010?\u001a\u00020>J\f\u0010@\u001a\b\u0012\u0004\u0012\u00020\u00050AJ\t\u0010B\u001a\u00020\u0003H\u00c6\u0003J\t\u0010C\u001a\u00020\u0010H\u00c6\u0003J\t\u0010D\u001a\u00020\u0010H\u00c6\u0003J\t\u0010E\u001a\u00020\u0010H\u00c6\u0003J\u000b\u0010F\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010G\u001a\u00020\u0015H\u00c6\u0003J\t\u0010H\u001a\u00020\u0015H\u00c6\u0003J\t\u0010I\u001a\u00020\u0005H\u00c6\u0003J\t\u0010J\u001a\u00020\u0007H\u00c6\u0003J\t\u0010K\u001a\u00020\u0007H\u00c6\u0003J\t\u0010L\u001a\u00020\u0007H\u00c6\u0003J\u000f\u0010M\u001a\b\u0012\u0004\u0012\u00020\u00050\u000bH\u00c6\u0003J\t\u0010N\u001a\u00020\u0005H\u00c6\u0003J\t\u0010O\u001a\u00020\u0007H\u00c6\u0003J\t\u0010P\u001a\u00020\u0007H\u00c6\u0003J\u0018\u0010Q\u001a\u00020\u00052\u0006\u0010R\u001a\u00020\u00052\u0006\u0010S\u001a\u00020TH\u0002J\u0018\u0010U\u001a\u00020\u00052\u0006\u0010V\u001a\u00020\u00052\u0006\u0010S\u001a\u00020TH\u0002J\u00a7\u0001\u0010W\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\b\b\u0002\u0010\t\u001a\u00020\u00072\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00050\u000b2\b\b\u0002\u0010\f\u001a\u00020\u00052\b\b\u0002\u0010\r\u001a\u00020\u00072\b\b\u0002\u0010\u000e\u001a\u00020\u00072\b\b\u0002\u0010\u000f\u001a\u00020\u00102\b\b\u0002\u0010\u0011\u001a\u00020\u00102\b\b\u0002\u0010\u0012\u001a\u00020\u00102\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u0014\u001a\u00020\u00152\b\b\u0002\u0010\u0016\u001a\u00020\u0015H\u00c6\u0001J\u0013\u0010X\u001a\u00020\u00102\b\u0010Y\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u000e\u0010Z\u001a\u00020[2\u0006\u0010\\\u001a\u00020\u0005J\t\u0010]\u001a\u00020[H\u00d6\u0001J\u001e\u0010^\u001a\u00020\u00102\u0006\u0010\\\u001a\u00020\u00052\u0006\u0010_\u001a\u00020[2\u0006\u0010`\u001a\u00020\u0007J\u000e\u0010a\u001a\u00020b2\u0006\u0010S\u001a\u00020TJ\u000e\u0010c\u001a\u00020b2\u0006\u0010S\u001a\u00020TJ\t\u0010d\u001a\u00020\u0003H\u00d6\u0001J\u0006\u0010e\u001a\u00020bJ\u0016\u0010f\u001a\u00020b2\u0006\u0010_\u001a\u00020[2\u0006\u0010g\u001a\u00020\u0005J\u0010\u0010h\u001a\u00020b2\u0006\u0010i\u001a\u00020\u0005H\u0002J\u0018\u0010j\u001a\u00020b2\u0006\u0010k\u001a\u00020\u00052\u0006\u0010l\u001a\u00020\u0005H\u0002J\u0006\u0010m\u001a\u00020bR\u001a\u0010\f\u001a\u00020\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0018\u0010\u0019\"\u0004\b\u001a\u0010\u001bR\u001a\u0010\u000e\u001a\u00020\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001c\u0010\u001d\"\u0004\b\u001e\u0010\u001fR\u001a\u0010\r\u001a\u00020\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b \u0010\u001d\"\u0004\b!\u0010\u001fR\u001a\u0010\u0004\u001a\u00020\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\"\u0010\u0019\"\u0004\b#\u0010\u001bR\u001a\u0010\u0014\u001a\u00020\u0015X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b$\u0010%\"\u0004\b&\u0010\'R\u001a\u0010\b\u001a\u00020\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b(\u0010\u001d\"\u0004\b)\u0010\u001fR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010+R\u001a\u0010\u0012\u001a\u00020\u0010X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0012\u0010,\"\u0004\b-\u0010.R\u001a\u0010\u0011\u001a\u00020\u0010X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0011\u0010,\"\u0004\b/\u0010.R\u001a\u0010\u000f\u001a\u00020\u0010X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000f\u0010,\"\u0004\b0\u0010.R\u001a\u0010\u0016\u001a\u00020\u0015X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b1\u0010%\"\u0004\b2\u0010\'R\u001a\u0010\t\u001a\u00020\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b3\u0010\u001d\"\u0004\b4\u0010\u001fR\u001c\u0010\u0013\u001a\u0004\u0018\u00010\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b5\u0010\u0019\"\u0004\b6\u0010\u001bR \u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00050\u000bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b7\u00108\"\u0004\b9\u0010:R\u001a\u0010\u0006\u001a\u00020\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b;\u0010\u001d\"\u0004\b<\u0010\u001f\u00a8\u0006n"}, d2 = {"Lcom/touptek/measurerealize/utils/ThreeRectangleMeasurement;", "", "id", "", "centerPoint", "Landroid/graphics/PointF;", "width", "", "height", "rotationAngle", "viewControlPoints", "", "bitmapCenterPoint", "bitmapWidth", "bitmapHeight", "isSelected", "", "isEditing", "isCompleted", "textPosition", "creationTime", "", "lastModified", "(Ljava/lang/String;Landroid/graphics/PointF;FFFLjava/util/List;Landroid/graphics/PointF;FFZZZLandroid/graphics/PointF;JJ)V", "getBitmapCenterPoint", "()Landroid/graphics/PointF;", "setBitmapCenterPoint", "(Landroid/graphics/PointF;)V", "getBitmapHeight", "()F", "setBitmapHeight", "(F)V", "getBitmapWidth", "setBitmapWidth", "getCenterPoint", "setCenterPoint", "getCreationTime", "()J", "setCreationTime", "(J)V", "getHeight", "setHeight", "getId", "()Ljava/lang/String;", "()Z", "setCompleted", "(Z)V", "setEditing", "setSelected", "getLastModified", "setLastModified", "getRotationAngle", "setRotationAngle", "getTextPosition", "setTextPosition", "getViewControlPoints", "()Ljava/util/List;", "setViewControlPoints", "(Ljava/util/List;)V", "getWidth", "setWidth", "calculateArea", "", "calculatePerimeter", "calculateRotatedCorners", "", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "convertBitmapToViewCoords", "bitmapPoint", "imageView", "Lcom/touptek/measurerealize/TpImageView;", "convertViewToBitmapCoords", "viewPoint", "copy", "equals", "other", "getNearestControlPointIndex", "", "touchPoint", "hashCode", "isControlPointInTouchRange", "pointIndex", "touchRadius", "syncBitmapCoords", "", "syncViewCoords", "toString", "updateControlPointsFromGeometry", "updateGeometryFromControlPoints", "newPoint", "updateRotationFromControlPoint", "controlPoint", "updateSizeFromTwoPoints", "point1", "point2", "updateTextPosition", "app_debug"})
public final class ThreeRectangleMeasurement {
    @org.jetbrains.annotations.NotNull
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull
    private android.graphics.PointF centerPoint;
    private float width;
    private float height;
    private float rotationAngle;
    @org.jetbrains.annotations.NotNull
    private java.util.List<android.graphics.PointF> viewControlPoints;
    @org.jetbrains.annotations.NotNull
    private android.graphics.PointF bitmapCenterPoint;
    private float bitmapWidth;
    private float bitmapHeight;
    private boolean isSelected;
    private boolean isEditing;
    private boolean isCompleted;
    @org.jetbrains.annotations.Nullable
    private android.graphics.PointF textPosition;
    private long creationTime;
    private long lastModified;
    
    /**
     * 🎯 三点矩形测量实例 - 支持旋转的专业级数据结构
     */
    @org.jetbrains.annotations.NotNull
    public final com.touptek.measurerealize.utils.ThreeRectangleMeasurement copy(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    android.graphics.PointF centerPoint, float width, float height, float rotationAngle, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> viewControlPoints, @org.jetbrains.annotations.NotNull
    android.graphics.PointF bitmapCenterPoint, float bitmapWidth, float bitmapHeight, boolean isSelected, boolean isEditing, boolean isCompleted, @org.jetbrains.annotations.Nullable
    android.graphics.PointF textPosition, long creationTime, long lastModified) {
        return null;
    }
    
    /**
     * 🎯 三点矩形测量实例 - 支持旋转的专业级数据结构
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 🎯 三点矩形测量实例 - 支持旋转的专业级数据结构
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 🎯 三点矩形测量实例 - 支持旋转的专业级数据结构
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public ThreeRectangleMeasurement() {
        super();
    }
    
    public ThreeRectangleMeasurement(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    android.graphics.PointF centerPoint, float width, float height, float rotationAngle, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> viewControlPoints, @org.jetbrains.annotations.NotNull
    android.graphics.PointF bitmapCenterPoint, float bitmapWidth, float bitmapHeight, boolean isSelected, boolean isEditing, boolean isCompleted, @org.jetbrains.annotations.Nullable
    android.graphics.PointF textPosition, long creationTime, long lastModified) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final android.graphics.PointF component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final android.graphics.PointF getCenterPoint() {
        return null;
    }
    
    public final void setCenterPoint(@org.jetbrains.annotations.NotNull
    android.graphics.PointF p0) {
    }
    
    public final float component3() {
        return 0.0F;
    }
    
    public final float getWidth() {
        return 0.0F;
    }
    
    public final void setWidth(float p0) {
    }
    
    public final float component4() {
        return 0.0F;
    }
    
    public final float getHeight() {
        return 0.0F;
    }
    
    public final void setHeight(float p0) {
    }
    
    public final float component5() {
        return 0.0F;
    }
    
    public final float getRotationAngle() {
        return 0.0F;
    }
    
    public final void setRotationAngle(float p0) {
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> getViewControlPoints() {
        return null;
    }
    
    public final void setViewControlPoints(@org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> p0) {
    }
    
    @org.jetbrains.annotations.NotNull
    public final android.graphics.PointF component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final android.graphics.PointF getBitmapCenterPoint() {
        return null;
    }
    
    public final void setBitmapCenterPoint(@org.jetbrains.annotations.NotNull
    android.graphics.PointF p0) {
    }
    
    public final float component8() {
        return 0.0F;
    }
    
    public final float getBitmapWidth() {
        return 0.0F;
    }
    
    public final void setBitmapWidth(float p0) {
    }
    
    public final float component9() {
        return 0.0F;
    }
    
    public final float getBitmapHeight() {
        return 0.0F;
    }
    
    public final void setBitmapHeight(float p0) {
    }
    
    public final boolean component10() {
        return false;
    }
    
    public final boolean isSelected() {
        return false;
    }
    
    public final void setSelected(boolean p0) {
    }
    
    public final boolean component11() {
        return false;
    }
    
    public final boolean isEditing() {
        return false;
    }
    
    public final void setEditing(boolean p0) {
    }
    
    public final boolean component12() {
        return false;
    }
    
    public final boolean isCompleted() {
        return false;
    }
    
    public final void setCompleted(boolean p0) {
    }
    
    @org.jetbrains.annotations.Nullable
    public final android.graphics.PointF component13() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final android.graphics.PointF getTextPosition() {
        return null;
    }
    
    public final void setTextPosition(@org.jetbrains.annotations.Nullable
    android.graphics.PointF p0) {
    }
    
    public final long component14() {
        return 0L;
    }
    
    public final long getCreationTime() {
        return 0L;
    }
    
    public final void setCreationTime(long p0) {
    }
    
    public final long component15() {
        return 0L;
    }
    
    public final long getLastModified() {
        return 0L;
    }
    
    public final void setLastModified(long p0) {
    }
    
    /**
     * 🎯 计算旋转后的四个角点坐标
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> calculateRotatedCorners() {
        return null;
    }
    
    /**
     * 🎯 计算面积（宽度 × 高度）
     */
    public final double calculateArea() {
        return 0.0;
    }
    
    /**
     * 🎯 计算周长（2 × (宽度 + 高度)）
     */
    public final double calculatePerimeter() {
        return 0.0;
    }
    
    /**
     * 🎯 从中心点、尺寸、角度更新控制点位置
     */
    public final void updateControlPointsFromGeometry() {
    }
    
    /**
     * 🎯 从控制点位置反推中心点、尺寸、角度
     */
    public final void updateGeometryFromControlPoints(int pointIndex, @org.jetbrains.annotations.NotNull
    android.graphics.PointF newPoint) {
    }
    
    /**
     * 🎯 从两个对角点更新尺寸和中心点
     */
    private final void updateSizeFromTwoPoints(android.graphics.PointF point1, android.graphics.PointF point2) {
    }
    
    /**
     * 🎯 从右上角控制点更新旋转角度
     */
    private final void updateRotationFromControlPoint(android.graphics.PointF controlPoint) {
    }
    
    /**
     * 🎯 更新文本位置 - 矩形中心
     */
    public final void updateTextPosition() {
    }
    
    /**
     * 🎯 检查点是否在控制点触摸范围内
     */
    public final boolean isControlPointInTouchRange(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint, int pointIndex, float touchRadius) {
        return false;
    }
    
    /**
     * 🎯 获取最近的控制点索引
     */
    public final int getNearestControlPointIndex(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return 0;
    }
    
    /**
     * 🔄 同步位图坐标 - 从视图坐标转换到位图坐标
     */
    public final void syncBitmapCoords(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.TpImageView imageView) {
    }
    
    /**
     * 🔄 同步视图坐标 - 从位图坐标转换到视图坐标
     */
    public final void syncViewCoords(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.TpImageView imageView) {
    }
    
    /**
     * 🔄 坐标转换方法 - 视图坐标到位图坐标
     */
    private final android.graphics.PointF convertViewToBitmapCoords(android.graphics.PointF viewPoint, com.touptek.measurerealize.TpImageView imageView) {
        return null;
    }
    
    /**
     * 🔄 坐标转换方法 - 位图坐标到视图坐标
     */
    private final android.graphics.PointF convertBitmapToViewCoords(android.graphics.PointF bitmapPoint, com.touptek.measurerealize.TpImageView imageView) {
        return null;
    }
}