<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_rounded_dialog_light"
    android:elevation="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:divider="@drawable/divider_vertical_light"
        android:showDividers="middle"
        android:padding="8dp">

        <!-- 左侧导航栏 -->
        <LinearLayout
            android:layout_width="180dp"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:background="?attr/selectableItemBackground">

            <TextView
                android:id="@+id/item_format"
                style="@style/ModernSettingTab"
                android:text="图像格式"/>

            <TextView
                android:id="@+id/item_video"
                style="@style/ModernSettingTab"
                android:text="录像设置"/>

            <TextView
                android:id="@+id/item_misc"
                style="@style/ModernSettingTab"
                android:text="高级选项"/>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginVertical="16dp"
                android:background="#EBEBEB"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/test_button"
                style="@style/ModernActionButton"
                android:text="切换至TV模式"/>
        </LinearLayout>

        <!-- 右侧内容容器 -->
        <FrameLayout
            android:id="@+id/content_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </LinearLayout>

    <com.google.android.material.card.MaterialCardView
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_margin="16dp"
        android:layout_gravity="top|end"
        app:cardBackgroundColor="@color/gray_text_disabled"
        app:cardCornerRadius="16dp"
        app:strokeColor="#20000000"
        app:strokeWidth="0.5dp">

        <ImageButton
            android:id="@+id/btn_close"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/ic_close"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:tint="#666666"/>
    </com.google.android.material.card.MaterialCardView>
</FrameLayout>