<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="autoae_layout" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\autoae_layout.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/autoae_layout_0" view="LinearLayout"><Expressions/><location startLine="0" startOffset="0" endLine="229" endOffset="14"/></Target><Target id="@+id/AECheckBox" view="CheckBox"><Expressions/><location startLine="10" startOffset="4" endLine="16" endOffset="46"/></Target><Target id="@+id/text_compensation_value" view="TextView"><Expressions/><location startLine="38" startOffset="12" endLine="46" endOffset="41"/></Target><Target id="@+id/btn_reduce_exposure_compensation" view="ImageButton"><Expressions/><location startLine="55" startOffset="12" endLine="61" endOffset="47"/></Target><Target id="@+id/seekbar_compensation_tv" view="SeekBar"><Expressions/><location startLine="63" startOffset="12" endLine="71" endOffset="57"/></Target><Target id="@+id/btn_add_exposure_compensation" view="ImageButton"><Expressions/><location startLine="73" startOffset="12" endLine="79" endOffset="47"/></Target><Target id="@+id/text_exposure_time_value" view="TextView"><Expressions/><location startLine="102" startOffset="12" endLine="110" endOffset="41"/></Target><Target id="@+id/btn_reduce_exposure_time" view="ImageButton"><Expressions/><location startLine="121" startOffset="12" endLine="127" endOffset="47"/></Target><Target id="@+id/seekbar_exposure_time_tv" view="SeekBar"><Expressions/><location startLine="129" startOffset="12" endLine="137" endOffset="57"/></Target><Target id="@+id/btn_add_exposure_time" view="ImageButton"><Expressions/><location startLine="139" startOffset="12" endLine="145" endOffset="47"/></Target><Target id="@+id/text_gain_value" view="TextView"><Expressions/><location startLine="168" startOffset="12" endLine="176" endOffset="41"/></Target><Target id="@+id/btn_reduce_gain" view="ImageButton"><Expressions/><location startLine="185" startOffset="12" endLine="191" endOffset="47"/></Target><Target id="@+id/seekbar_gain_tv" view="SeekBar"><Expressions/><location startLine="193" startOffset="12" endLine="201" endOffset="57"/></Target><Target id="@+id/btn_add_gain" view="ImageButton"><Expressions/><location startLine="203" startOffset="12" endLine="209" endOffset="47"/></Target><Target id="@+id/btn_Default_ae" view="Button"><Expressions/><location startLine="220" startOffset="8" endLine="224" endOffset="36"/></Target></Targets></Layout>