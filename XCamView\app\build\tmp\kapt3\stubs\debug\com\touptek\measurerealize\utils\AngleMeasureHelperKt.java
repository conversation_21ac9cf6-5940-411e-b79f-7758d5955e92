package com.touptek.measurerealize.utils;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 2, d1 = {"\u0000\u0012\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a\u0016\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u0004\u001a\u0016\u0010\u0005\u001a\u00020\u00012\u0006\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u0004\u00a8\u0006\u0007"}, d2 = {"convertBitmapToViewCoords", "Landroid/graphics/PointF;", "bitmapPoint", "imageView", "Landroid/widget/ImageView;", "convertViewToBitmapCoords", "viewPoint", "app_debug"})
public final class AngleMeasureHelperKt {
    
    @org.jetbrains.annotations.NotNull
    public static final android.graphics.PointF convertViewToBitmapCoords(@org.jetbrains.annotations.NotNull
    android.graphics.PointF viewPoint, @org.jetbrains.annotations.NotNull
    android.widget.ImageView imageView) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public static final android.graphics.PointF convertBitmapToViewCoords(@org.jetbrains.annotations.NotNull
    android.graphics.PointF bitmapPoint, @org.jetbrains.annotations.NotNull
    android.widget.ImageView imageView) {
        return null;
    }
}