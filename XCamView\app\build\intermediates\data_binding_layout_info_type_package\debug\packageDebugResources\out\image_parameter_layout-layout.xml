<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="image_parameter_layout" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\image_parameter_layout.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/image_parameter_layout_0" view="LinearLayout"><Expressions/><location startLine="0" startOffset="0" endLine="278" endOffset="14"/></Target><Target id="@+id/text_saturation_value" view="TextView"><Expressions/><location startLine="28" startOffset="12" endLine="36" endOffset="41"/></Target><Target id="@+id/btn_saturation_reduce" view="ImageButton"><Expressions/><location startLine="45" startOffset="12" endLine="51" endOffset="47"/></Target><Target id="@+id/seekbar_saturation_tv" view="SeekBar"><Expressions/><location startLine="53" startOffset="12" endLine="60" endOffset="57"/></Target><Target id="@+id/btn_saturation_add" view="ImageButton"><Expressions/><location startLine="62" startOffset="12" endLine="68" endOffset="47"/></Target><Target id="@+id/text_gamma_value" view="TextView"><Expressions/><location startLine="91" startOffset="12" endLine="99" endOffset="41"/></Target><Target id="@+id/btn_gamma_reduce" view="ImageButton"><Expressions/><location startLine="109" startOffset="12" endLine="115" endOffset="47"/></Target><Target id="@+id/seekbar_gamma_tv" view="SeekBar"><Expressions/><location startLine="117" startOffset="12" endLine="124" endOffset="57"/></Target><Target id="@+id/btn_gamma_add" view="ImageButton"><Expressions/><location startLine="126" startOffset="12" endLine="132" endOffset="47"/></Target><Target id="@+id/text_contrast_value" view="TextView"><Expressions/><location startLine="155" startOffset="12" endLine="163" endOffset="41"/></Target><Target id="@+id/btn_contrast_reduce" view="ImageButton"><Expressions/><location startLine="172" startOffset="12" endLine="178" endOffset="47"/></Target><Target id="@+id/seekbar_contrast_tv" view="SeekBar"><Expressions/><location startLine="180" startOffset="12" endLine="187" endOffset="57"/></Target><Target id="@+id/btn_contrast_add" view="ImageButton"><Expressions/><location startLine="189" startOffset="12" endLine="195" endOffset="47"/></Target><Target id="@+id/text_brightness_value" view="TextView"><Expressions/><location startLine="218" startOffset="12" endLine="226" endOffset="41"/></Target><Target id="@+id/btn_brightness_reduce" view="ImageButton"><Expressions/><location startLine="235" startOffset="12" endLine="241" endOffset="47"/></Target><Target id="@+id/seekbar_brightness_tv" view="SeekBar"><Expressions/><location startLine="243" startOffset="12" endLine="250" endOffset="57"/></Target><Target id="@+id/btn_brightness_add" view="ImageButton"><Expressions/><location startLine="252" startOffset="12" endLine="258" endOffset="47"/></Target><Target id="@+id/btn_Default_image_parameter" view="Button"><Expressions/><location startLine="269" startOffset="8" endLine="273" endOffset="36"/></Target></Targets></Layout>