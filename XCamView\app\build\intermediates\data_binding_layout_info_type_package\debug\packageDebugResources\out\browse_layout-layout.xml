<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="browse_layout" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\browse_layout.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/browse_layout_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="301" endOffset="51"/></Target><Target id="@+id/right_panel" view="FrameLayout"><Expressions/><location startLine="9" startOffset="4" endLine="26" endOffset="17"/></Target><Target id="@+id/toolbar" view="LinearLayout"><Expressions/><location startLine="28" startOffset="4" endLine="105" endOffset="18"/></Target><Target id="@+id/browse_select_all" view="TextView"><Expressions/><location startLine="63" startOffset="12" endLine="75" endOffset="38"/></Target><Target id="@+id/browse_delete" view="TextView"><Expressions/><location startLine="78" startOffset="12" endLine="90" endOffset="38"/></Target><Target id="@+id/browse_config" view="ImageView"><Expressions/><location startLine="93" startOffset="8" endLine="103" endOffset="44"/></Target><Target id="@+id/recycler_container" view="FrameLayout"><Expressions/><location startLine="107" startOffset="4" endLine="122" endOffset="17"/></Target><Target id="@+id/recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="116" startOffset="8" endLine="120" endOffset="42"/></Target><Target id="@+id/bottom_action_bar" view="LinearLayout"><Expressions/><location startLine="125" startOffset="4" endLine="300" endOffset="18"/></Target><Target id="@+id/btn_container1" view="LinearLayout"><Expressions/><location startLine="147" startOffset="8" endLine="175" endOffset="22"/></Target><Target id="@+id/btn_action1" view="TextView"><Expressions/><location startLine="164" startOffset="12" endLine="174" endOffset="42"/></Target><Target id="@+id/btn_container2" view="LinearLayout"><Expressions/><location startLine="178" startOffset="8" endLine="206" endOffset="22"/></Target><Target id="@+id/btn_action2" view="TextView"><Expressions/><location startLine="195" startOffset="12" endLine="205" endOffset="42"/></Target><Target id="@+id/btn_container3" view="LinearLayout"><Expressions/><location startLine="209" startOffset="8" endLine="237" endOffset="22"/></Target><Target id="@+id/btn_action3" view="TextView"><Expressions/><location startLine="226" startOffset="12" endLine="236" endOffset="42"/></Target><Target id="@+id/btn_container4" view="LinearLayout"><Expressions/><location startLine="240" startOffset="8" endLine="268" endOffset="22"/></Target><Target id="@+id/btn_action4" view="TextView"><Expressions/><location startLine="257" startOffset="12" endLine="267" endOffset="42"/></Target><Target id="@+id/btn_container5" view="LinearLayout"><Expressions/><location startLine="271" startOffset="8" endLine="299" endOffset="22"/></Target><Target id="@+id/btn_action5" view="TextView"><Expressions/><location startLine="288" startOffset="12" endLine="298" endOffset="42"/></Target></Targets></Layout>