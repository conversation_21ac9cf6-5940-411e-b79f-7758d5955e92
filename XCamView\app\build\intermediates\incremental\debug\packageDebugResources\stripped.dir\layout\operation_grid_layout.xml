<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp">

    <!-- 图片和覆盖层容器 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- 图片 -->
        <ImageView
            android:id="@+id/grid_image"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:adjustViewBounds="true"
            android:scaleType="fitCenter"/>

        <!-- 覆盖层（必须放在 ImageView 后面以覆盖在上方） -->
        <View
            android:id="@+id/selected_overlay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#60010101"
            android:visibility="invisible"/>

        <ImageView
            android:id="@+id/video_indicator"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_video_triangle"
            android:visibility="gone"/>

        <ImageView
            android:id="@+id/grid_checkbox"
            android:layout_width="42dp"
            android:layout_height="42dp"
            android:layout_gravity="top|right"
            android:layout_marginRight="4dp"
            android:layout_marginTop="28dp"
            android:visibility="invisible"
            android:src="@drawable/ic_unchecked"/>

    </FrameLayout>

    <!-- 文本标签 -->
    <TextView
        android:id="@+id/grid_label"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:maxLines="1"
        android:ellipsize="end"
        android:padding="4dp"
        android:textColor="@android:color/black"
        android:textSize="14sp"/>

</LinearLayout>