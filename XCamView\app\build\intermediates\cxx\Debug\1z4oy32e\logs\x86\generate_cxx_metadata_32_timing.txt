# C/C++ build system timings
generate_cxx_metadata
  execute-generate-process
    exec-configure 595ms
    [gap of 11ms]
  execute-generate-process completed in 608ms
  [gap of 31ms]
generate_cxx_metadata completed in 670ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 38ms]
  create-invalidation-state 101ms
  [gap of 49ms]
  write-metadata-json-to-file 12ms
generate_cxx_metadata completed in 201ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 65ms
  [gap of 36ms]
generate_cxx_metadata completed in 120ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 40ms]
  create-invalidation-state 94ms
  [gap of 32ms]
generate_cxx_metadata completed in 166ms

# C/C++ build system timings

