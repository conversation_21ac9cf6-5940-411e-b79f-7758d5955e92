package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 🎯 三点圆测量助手 - 专业级测量管理类
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000j\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000e\n\u0002\b\u0003\u0018\u0000 62\u00020\u0001:\u00016B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0015\u001a\u00020\u0010J\u0006\u0010\u0016\u001a\u00020\u0010J\u0006\u0010\u0017\u001a\u00020\bJ\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00130\u0019J\u0006\u0010\u001a\u001a\u00020\u0004J\b\u0010\u001b\u001a\u0004\u0018\u00010\u0013J \u0010\u001c\u001a\u00020\b2\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020\u00042\u0006\u0010 \u001a\u00020\u0004H\u0002J\u001e\u0010!\u001a\u00020\b2\u0006\u0010\"\u001a\u00020#2\u0006\u0010\u001f\u001a\u00020\u00042\u0006\u0010 \u001a\u00020\u0004J\u0010\u0010$\u001a\u00020\b2\u0006\u0010\u001d\u001a\u00020\u001eH\u0002J \u0010%\u001a\u00020\b2\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020\u00042\u0006\u0010 \u001a\u00020\u0004H\u0002J\u0006\u0010&\u001a\u00020\bJ\u0016\u0010\'\u001a\u00020\u00102\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010(\u001a\u00020)J\u0006\u0010\u0007\u001a\u00020\bJ \u0010*\u001a\u00020\b2\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020\u00042\u0006\u0010 \u001a\u00020\u0004H\u0002J\u000e\u0010+\u001a\u00020\b2\u0006\u0010\u001d\u001a\u00020\u001eJ\b\u0010,\u001a\u00020\u0010H\u0002J\u0006\u0010-\u001a\u00020\u0010J\u0006\u0010.\u001a\u00020\u0010J\b\u0010/\u001a\u00020\u0010H\u0002J\u0006\u00100\u001a\u00020\u0010J\u0014\u00101\u001a\u00020\u00102\f\u00102\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fJ\u0006\u00103\u001a\u000204J\u0006\u00105\u001a\u00020\u0010R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000e\u001a\n\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00130\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0014\u001a\u0004\u0018\u00010\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00067"}, d2 = {"Lcom/touptek/measurerealize/utils/ThreeCircleMeasureHelper;", "", "()V", "draggedPointIndex", "", "imageView", "Lcom/touptek/measurerealize/TpImageView;", "isDraggingPoint", "", "lastTouchX", "", "lastTouchY", "longPressStartTime", "", "measurementUpdateCallback", "Lkotlin/Function0;", "", "measurements", "", "Lcom/touptek/measurerealize/utils/ThreeCircleMeasurement;", "selectedMeasurement", "clearAllMeasurements", "clearSelection", "deleteSelectedMeasurement", "getAllMeasurementData", "", "getMeasurementCount", "getSelectedMeasurement", "handleTouchDown", "touchPoint", "Landroid/graphics/PointF;", "viewWidth", "viewHeight", "handleTouchEvent", "event", "Landroid/view/MotionEvent;", "handleTouchMove", "handleTouchUp", "hasSelectedMeasurement", "init", "bitmap", "Landroid/graphics/Bitmap;", "isInImageContentArea", "isNearAnyMeasurement", "notifyUpdate", "onScaleChanged", "pauseMeasurement", "resetInteractionState", "resumeMeasurement", "setMeasurementUpdateCallback", "callback", "startNewMeasurement", "", "stopMeasurement", "Companion", "app_debug"})
public final class ThreeCircleMeasureHelper {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.measurerealize.utils.ThreeCircleMeasureHelper.Companion Companion = null;
    private static final java.lang.String TAG = "ThreeCircleMeasureHelper";
    private static final float TOUCH_RADIUS = 80.0F;
    private static final long LONG_PRESS_DURATION = 800L;
    private static final float CLICK_TOLERANCE = 20.0F;
    private final java.util.List<com.touptek.measurerealize.utils.ThreeCircleMeasurement> measurements = null;
    private com.touptek.measurerealize.TpImageView imageView;
    private kotlin.jvm.functions.Function0<kotlin.Unit> measurementUpdateCallback;
    private com.touptek.measurerealize.utils.ThreeCircleMeasurement selectedMeasurement;
    private boolean isDraggingPoint = false;
    private int draggedPointIndex = -1;
    private long longPressStartTime = 0L;
    private float lastTouchX = 0.0F;
    private float lastTouchY = 0.0F;
    
    public ThreeCircleMeasureHelper() {
        super();
    }
    
    /**
     * 🎯 初始化助手
     */
    public final void init(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.TpImageView imageView, @org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
    }
    
    /**
     * 🎯 设置测量更新回调
     */
    public final void setMeasurementUpdateCallback(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    /**
     * 🎯 开始新的三点圆测量 - 在屏幕中央创建三点圆
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String startNewMeasurement() {
        return null;
    }
    
    /**
     * 🎯 处理触摸事件
     */
    public final boolean handleTouchEvent(@org.jetbrains.annotations.NotNull
    android.view.MotionEvent event, int viewWidth, int viewHeight) {
        return false;
    }
    
    /**
     * 🎯 处理触摸按下事件
     */
    private final boolean handleTouchDown(android.graphics.PointF touchPoint, int viewWidth, int viewHeight) {
        return false;
    }
    
    /**
     * 🎯 处理触摸移动事件
     */
    private final boolean handleTouchMove(android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🎯 处理触摸抬起事件
     */
    private final boolean handleTouchUp(android.graphics.PointF touchPoint, int viewWidth, int viewHeight) {
        return false;
    }
    
    /**
     * 🎯 智能UI区域保护 - 避免UI按钮区域被误判
     */
    private final boolean isInImageContentArea(android.graphics.PointF touchPoint, int viewWidth, int viewHeight) {
        return false;
    }
    
    /**
     * 🎯 获取所有测量数据用于覆盖层显示
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.touptek.measurerealize.utils.ThreeCircleMeasurement> getAllMeasurementData() {
        return null;
    }
    
    /**
     * 🎯 获取测量数量
     */
    public final int getMeasurementCount() {
        return 0;
    }
    
    /**
     * 🎯 检查是否有选中的测量
     */
    public final boolean hasSelectedMeasurement() {
        return false;
    }
    
    /**
     * 🎯 获取选中的测量
     */
    @org.jetbrains.annotations.Nullable
    public final com.touptek.measurerealize.utils.ThreeCircleMeasurement getSelectedMeasurement() {
        return null;
    }
    
    /**
     * 🎯 检查是否正在拖拽点
     */
    public final boolean isDraggingPoint() {
        return false;
    }
    
    /**
     * 🎯 检查是否靠近任何测量
     */
    public final boolean isNearAnyMeasurement(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🗑️ 删除选中的测量 - 智能删除逻辑
     */
    public final boolean deleteSelectedMeasurement() {
        return false;
    }
    
    /**
     * 🔄 清除所有选中状态
     */
    public final void clearSelection() {
    }
    
    /**
     * 🔄 重置交互状态
     */
    private final void resetInteractionState() {
    }
    
    /**
     * 🎯 暂停测量 - 保持数据但停止交互
     */
    public final void pauseMeasurement() {
    }
    
    /**
     * 🎯 恢复测量 - 从暂停状态恢复到可编辑状态
     */
    public final void resumeMeasurement() {
    }
    
    /**
     * 🎯 缩放变化时同步坐标
     */
    public final void onScaleChanged() {
    }
    
    /**
     * 🎯 通知更新
     */
    private final void notifyUpdate() {
    }
    
    /**
     * 🎯 停止测量
     */
    public final void stopMeasurement() {
    }
    
    /**
     * 🎯 清除所有测量数据
     */
    public final void clearAllMeasurements() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/touptek/measurerealize/utils/ThreeCircleMeasureHelper$Companion;", "", "()V", "CLICK_TOLERANCE", "", "LONG_PRESS_DURATION", "", "TAG", "", "TOUCH_RADIUS", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}