<ScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 存储设备选择 - 带边框的区块 -->

        <!-- 模式选择 - 带边框的区块 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/border_box">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="模式选择"
                android:textSize="18sp"
                android:textStyle="bold"/>

            <RadioGroup
                android:id="@+id/mode_radio_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp">

                <RadioButton
                    android:id="@+id/radio_A"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="低延时模式"/>

                <RadioButton
                    android:id="@+id/radio_B"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="高帧率模式"/>
            </RadioGroup>
        </LinearLayout>

        <!-- 字体选择 - 带边框的区块 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/border_box">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="字体选择"
                android:textSize="18sp"
                android:textStyle="bold"/>

            <RadioGroup
                android:id="@+id/font_radio_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp">

                <RadioButton
                    android:id="@+id/radio_font_system"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="系统默认字体"/>

                <RadioButton
                    android:id="@+id/radio_font_song"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="宋体"/>

                <RadioButton
                    android:id="@+id/radio_font_kai"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="楷体"/>
            </RadioGroup>
        </LinearLayout>
    </LinearLayout>
</ScrollView>