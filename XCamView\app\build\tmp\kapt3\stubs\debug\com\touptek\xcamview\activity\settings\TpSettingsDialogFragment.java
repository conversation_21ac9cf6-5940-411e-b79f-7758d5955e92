package com.touptek.xcamview.activity.settings;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\t\n\u0002\u0010\u000e\n\u0002\b\t\u0018\u0000 \'2\u00020\u00012\u00020\u0002:\u0001\'B\u0005\u00a2\u0006\u0002\u0010\u0003J&\u0010\f\u001a\u0004\u0018\u00010\r2\u0006\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u0013H\u0016J\u0010\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u0013H\u0016J\b\u0010\u0017\u001a\u00020\u0015H\u0016J\b\u0010\u0018\u001a\u00020\u0015H\u0016J\u001a\u0010\u0019\u001a\u00020\u00152\u0006\u0010\u001a\u001a\u00020\r2\b\u0010\u0012\u001a\u0004\u0018\u00010\u0013H\u0016J\u0010\u0010\u001b\u001a\u00020\u00152\u0006\u0010\u001a\u001a\u00020\rH\u0002J\u0018\u0010\u001c\u001a\u00020\u00152\u0006\u0010\u001d\u001a\u00020\u00052\u0006\u0010\u001e\u001a\u00020\u001fH\u0002J\u0010\u0010 \u001a\u00020\u00152\u0006\u0010!\u001a\u00020\u001fH\u0002J\u0006\u0010\"\u001a\u00020\u0015J\u0018\u0010#\u001a\u00020\u00152\u0006\u0010$\u001a\u00020\u00072\u0006\u0010%\u001a\u00020\tH\u0002J\b\u0010&\u001a\u00020\u0015H\u0002R\u0010\u0010\u0004\u001a\u0004\u0018\u00010\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\n\u001a\u0004\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006("}, d2 = {"Lcom/touptek/xcamview/activity/settings/TpSettingsDialogFragment;", "Landroidx/fragment/app/DialogFragment;", "Lcom/touptek/xcamview/activity/settings/TpMiscSettingsFragment$OnModeChangeListener;", "()V", "currentFragment", "Landroidx/fragment/app/Fragment;", "currentTabId", "", "isCameraMode", "", "modeButton", "Landroid/widget/Button;", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onSaveInstanceState", "", "outState", "onStart", "onSwitchToTVMode", "onViewCreated", "view", "setupTabSelection", "showFragment", "fragment", "tag", "", "showToast", "message", "startTVMode", "switchTab", "tabId", "switchFragment", "updateTabSelection", "Companion", "app_debug"})
public final class TpSettingsDialogFragment extends androidx.fragment.app.DialogFragment implements com.touptek.xcamview.activity.settings.TpMiscSettingsFragment.OnModeChangeListener {
    private int currentTabId;
    private boolean isCameraMode = false;
    private android.widget.Button modeButton;
    private androidx.fragment.app.Fragment currentFragment;
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.xcamview.activity.settings.TpSettingsDialogFragment.Companion Companion = null;
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String TAG_NETWORK = "network_fragment";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String TAG_STORAGE = "storage_fragment";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String TAG_FORMAT = "format_fragment";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String TAG_VIDEO = "video_fragment";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String TAG_MEASUREMENT = "measurement_fragment";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String TAG_MISC = "misc_fragment";
    
    public TpSettingsDialogFragment() {
        super();
    }
    
    @java.lang.Override
    public void onSwitchToTVMode() {
    }
    
    @org.jetbrains.annotations.Nullable
    @java.lang.Override
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override
    public void onViewCreated(@org.jetbrains.annotations.NotNull
    android.view.View view, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupTabSelection(android.view.View view) {
    }
    
    private final void switchTab(int tabId, boolean switchFragment) {
    }
    
    private final void updateTabSelection() {
    }
    
    private final void showFragment(androidx.fragment.app.Fragment fragment, java.lang.String tag) {
    }
    
    @java.lang.Override
    public void onSaveInstanceState(@org.jetbrains.annotations.NotNull
    android.os.Bundle outState) {
    }
    
    @java.lang.Override
    public void onStart() {
    }
    
    public final void startTVMode() {
    }
    
    private final void showToast(java.lang.String message) {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0006\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/touptek/xcamview/activity/settings/TpSettingsDialogFragment$Companion;", "", "()V", "TAG_FORMAT", "", "TAG_MEASUREMENT", "TAG_MISC", "TAG_NETWORK", "TAG_STORAGE", "TAG_VIDEO", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}