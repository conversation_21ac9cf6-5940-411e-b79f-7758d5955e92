package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 📏 水平线测量助手类 - 专业级实现
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000x\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010!\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0011\u0018\u0000 =2\u00020\u0001:\u0001=B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010!\u001a\u00020\"J\u0006\u0010#\u001a\u00020\u001bJ\u0006\u0010$\u001a\u00020\u001bJ\u0006\u0010%\u001a\u00020\u001bJ\u0006\u0010&\u001a\u00020\rJ\f\u0010\'\u001a\b\u0012\u0004\u0012\u00020)0(J\u0006\u0010*\u001a\u00020\tJ\u001e\u0010+\u001a\u00020\r2\u0006\u0010,\u001a\u00020-2\u0006\u0010.\u001a\u00020\t2\u0006\u0010/\u001a\u00020\tJ\u0006\u00100\u001a\u00020\rJ\u0016\u00101\u001a\u00020\u001b2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0005\u001a\u00020\u0006J\u0006\u0010\f\u001a\u00020\rJ \u00102\u001a\u00020\r2\u0006\u00103\u001a\u00020\u00102\u0006\u0010.\u001a\u00020\t2\u0006\u0010/\u001a\u00020\tH\u0002J\u000e\u00104\u001a\u00020\r2\u0006\u00103\u001a\u00020\u0010J\u0018\u00105\u001a\u00020\r2\u0006\u00103\u001a\u00020\u00102\u0006\u00106\u001a\u00020\u0004H\u0002J\u000e\u00107\u001a\u00020\r2\u0006\u00103\u001a\u00020\u0010J\u0006\u00108\u001a\u00020\u001bJ\b\u00109\u001a\u00020\u001bH\u0002J\u0014\u0010:\u001a\u00020\u001b2\f\u0010;\u001a\b\u0012\u0004\u0012\u00020\u001b0\u001aJ\u0006\u0010<\u001a\u00020\"R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0016\u001a\u0004\u0018\u00010\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0019\u001a\n\u0012\u0004\u0012\u00020\u001b\u0018\u00010\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00040\u001dX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001e\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001f\u001a\u0004\u0018\u00010\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006>"}, d2 = {"Lcom/touptek/measurerealize/utils/HorizonLineMeasureHelper;", "", "()V", "activeMeasurement", "Lcom/touptek/measurerealize/utils/HorizonLineMeasurement;", "bitmap", "Landroid/graphics/Bitmap;", "draggedMeasurement", "draggedPointIndex", "", "imageView", "Landroid/widget/ImageView;", "isDraggingPoint", "", "isInitialized", "lastTouchPoint", "Landroid/graphics/PointF;", "lastTouchTime", "", "lastTouchX", "", "lastTouchY", "longPressRunnable", "Ljava/lang/Runnable;", "longPressStartTime", "measurementUpdateCallback", "Lkotlin/Function0;", "", "measurements", "", "selectedMeasurement", "touchDownPoint", "touchDownTime", "addNewMeasurement", "", "clearAllMeasurements", "clearAllSelections", "clearSelection", "deleteSelectedMeasurement", "getAllMeasurementData", "", "Lcom/touptek/measurerealize/utils/HorizonLineMeasurementData;", "getMeasurementCount", "handleTouchEvent", "event", "Landroid/view/MotionEvent;", "viewWidth", "viewHeight", "hasSelectedMeasurement", "init", "isInImageContentArea", "touchPoint", "isNearAnyMeasurement", "isPointOnLine", "measurement", "isPointOnMeasurement", "onScaleChanged", "resetInteractionState", "setMeasurementUpdateCallback", "callback", "startNewMeasurement", "Companion", "app_debug"})
public final class HorizonLineMeasureHelper {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.measurerealize.utils.HorizonLineMeasureHelper.Companion Companion = null;
    private static final java.lang.String TAG = "HorizonLineMeasureHelper";
    private static final float TOUCH_RADIUS = 80.0F;
    private static final float CLICK_TOLERANCE = 20.0F;
    private static final long LONG_PRESS_DURATION = 800L;
    private static final float DEFAULT_LINE_LENGTH = 200.0F;
    private android.widget.ImageView imageView;
    private android.graphics.Bitmap bitmap;
    private boolean isInitialized = false;
    private final java.util.List<com.touptek.measurerealize.utils.HorizonLineMeasurement> measurements = null;
    private kotlin.jvm.functions.Function0<kotlin.Unit> measurementUpdateCallback;
    private com.touptek.measurerealize.utils.HorizonLineMeasurement selectedMeasurement;
    private com.touptek.measurerealize.utils.HorizonLineMeasurement activeMeasurement;
    private boolean isDraggingPoint = false;
    private com.touptek.measurerealize.utils.HorizonLineMeasurement draggedMeasurement;
    private int draggedPointIndex = -1;
    private long lastTouchTime = 0L;
    private java.lang.Runnable longPressRunnable;
    private long longPressStartTime = 0L;
    private float lastTouchX = 0.0F;
    private float lastTouchY = 0.0F;
    private android.graphics.PointF lastTouchPoint;
    private long touchDownTime = 0L;
    private android.graphics.PointF touchDownPoint;
    
    public HorizonLineMeasureHelper() {
        super();
    }
    
    /**
     * 🚀 初始化助手
     */
    public final void init(@org.jetbrains.annotations.NotNull
    android.widget.ImageView imageView, @org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
    }
    
    /**
     * 🎯 设置测量更新回调
     */
    public final void setMeasurementUpdateCallback(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    /**
     * 📏 开始新的水平线测量 - 在屏幕中心创建默认水平线
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String startNewMeasurement() {
        return null;
    }
    
    /**
     * 📏 添加新的水平线测量（在测量模式下）
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String addNewMeasurement() {
        return null;
    }
    
    /**
     * 🎯 处理触摸事件 - 与LineMeasureHelper保持一致的交互逻辑
     */
    public final boolean handleTouchEvent(@org.jetbrains.annotations.NotNull
    android.view.MotionEvent event, int viewWidth, int viewHeight) {
        return false;
    }
    
    /**
     * 🎯 检查点是否在线段上
     */
    private final boolean isPointOnLine(android.graphics.PointF touchPoint, com.touptek.measurerealize.utils.HorizonLineMeasurement measurement) {
        return false;
    }
    
    /**
     * 🔄 缩放变化时的坐标同步
     */
    public final void onScaleChanged() {
    }
    
    /**
     * 🎯 检查是否有点在测量上
     */
    public final boolean isPointOnMeasurement(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🎯 检查是否靠近任何测量
     */
    public final boolean isNearAnyMeasurement(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🎯 检查是否正在拖拽点
     */
    public final boolean isDraggingPoint() {
        return false;
    }
    
    /**
     * 🔄 清除所有选中状态 - 与LineMeasureHelper保持一致
     */
    public final void clearAllSelections() {
    }
    
    /**
     * 🔄 清除选中状态
     */
    public final void clearSelection() {
    }
    
    /**
     * 🎯 检查是否有选中的测量
     */
    public final boolean hasSelectedMeasurement() {
        return false;
    }
    
    /**
     * 📊 获取测量数量
     */
    public final int getMeasurementCount() {
        return 0;
    }
    
    /**
     * 📊 获取所有测量数据（用于渲染）
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.touptek.measurerealize.utils.HorizonLineMeasurementData> getAllMeasurementData() {
        return null;
    }
    
    /**
     * 🧹 清理所有测量数据
     */
    public final void clearAllMeasurements() {
    }
    
    /**
     * 🔄 重置交互状态 - 与LineMeasureHelper保持一致
     */
    private final void resetInteractionState() {
    }
    
    /**
     * 🗑️ 删除选中的测量 - 与LineMeasureHelper保持一致
     */
    public final boolean deleteSelectedMeasurement() {
        return false;
    }
    
    /**
     * 🛡️ 检测触摸点是否在图像内容区域（避免UI按钮区域） - 与LineMeasureHelper保持一致
     */
    private final boolean isInImageContentArea(android.graphics.PointF touchPoint, int viewWidth, int viewHeight) {
        return false;
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/touptek/measurerealize/utils/HorizonLineMeasureHelper$Companion;", "", "()V", "CLICK_TOLERANCE", "", "DEFAULT_LINE_LENGTH", "LONG_PRESS_DURATION", "", "TAG", "", "TOUCH_RADIUS", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}