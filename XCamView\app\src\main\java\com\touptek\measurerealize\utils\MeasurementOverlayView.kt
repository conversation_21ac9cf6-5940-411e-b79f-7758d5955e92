package com.touptek.measurerealize.utils

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import android.view.MotionEvent
import android.widget.ImageView
import com.touptek.measurerealize.TpImageView
import com.touptek.measurerealize.utils.TwoCirclesMeasurement
import kotlin.math.*

/**
 * 🎨 专业级测量覆盖层 - 超越iPad体验的可视化
 * 
 * 核心特性：
 * - 多层测量渲染：支持同时显示多种测量类型
 * - 专业视觉效果：动态高亮、流畅动画、精美渲染
 * - 智能坐标转换：与TpImageView完美协作
 * - 高性能绘制：优化的Canvas绘制算法
 */
class MeasurementOverlayView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 🎨 专业级绘制工具
    private val linePaint = Paint().apply {
        color = Color.parseColor("#2196F3")
        strokeWidth = 6f
        style = Paint.Style.STROKE
        isAntiAlias = true
        strokeCap = Paint.Cap.ROUND
    }

    private val pointPaint = Paint().apply {
        color = Color.parseColor("#4CAF50")
        style = Paint.Style.FILL
        isAntiAlias = true
    }

    private val highlightPointPaint = Paint().apply {
        color = Color.parseColor("#FF5722")
        style = Paint.Style.FILL
        isAntiAlias = true
    }

    private val textPaint = Paint().apply {
        color = Color.WHITE
        textSize = 48f
        isAntiAlias = true
        typeface = Typeface.DEFAULT_BOLD
    }

    private val arcPaint = Paint().apply {
        color = Color.parseColor("#FF9800")
        strokeWidth = 4f
        style = Paint.Style.STROKE
        isAntiAlias = true
    }

    // 测量数据和图像视图引用
    private var measurementData: MeasurementData? = null
    private var allMeasurementData: List<AngleMeasurementData> = emptyList() // 支持多个角度测量
    private var allFourPointAngleMeasurementData: List<FourPointAngleMeasurementData> = emptyList() // 支持多个四点角度测量
    private var allPointMeasurementData: List<PointMeasurementData> = emptyList() // 支持多个点测量
    private var allLineMeasurementData: List<LineMeasurementData> = emptyList() // 支持多个线段测量
    private var allHorizonLineMeasurementData: List<HorizonLineMeasurementData> = emptyList() // 支持多个水平线测量
    private var allVerticalLineMeasurementData: List<VerticalLineMeasurementData> = emptyList() // 支持多个垂直线测量
    private var allParallelLinesMeasurementData: List<ParallelLinesMeasurementData> = emptyList() // 支持多个平行线测量
    private var allThreeVerticalMeasurementData: List<ThreeVerticalMeasurementData> = emptyList() // 支持多个三垂直测量
    private var allRectangleMeasurementData: List<RectangleMeasurementData> = emptyList() // 支持多个矩形测量
    private var allThreeRectangleMeasurementData: List<ThreeRectangleMeasurementData> = emptyList() // 支持多个三点矩形测量
    private var allEllipseMeasurementData: List<EllipseMeasurement> = emptyList() // 支持多个椭圆测量
    private var allCircleMeasurementData: List<CenterCircleMeasurement> = emptyList() // 支持多个圆测量
    private var allThreeCircleMeasurementData: List<ThreeCircleMeasurement> = emptyList() // 支持多个三点圆测量
    private var allTwoCirclesMeasurementData: List<TwoCirclesMeasurement> = emptyList() // 支持多个双圆测量
    private var imageView: TpImageView? = null

    // 测量助手引用
    private var angleMeasureHelper: AngleMeasureHelper? = null
    private var fourPointAngleHelper: FourPointAngleHelper? = null
    private var pointMeasureHelper: PointMeasureHelper? = null
    private var lineMeasureHelper: LineMeasureHelper? = null
    private var horizonLineMeasureHelper: HorizonLineMeasureHelper? = null
    private var verticalLineMeasureHelper: VerticalLineMeasureHelper? = null
    private var parallelLinesMeasureHelper: ParallelLinesMeasureHelper? = null
    private var threeVerticalMeasureHelper: ThreeVerticalMeasureHelper? = null
    private var rectangleMeasureHelper: RectangleMeasureHelper? = null
    private var threeRectangleMeasureHelper: ThreeRectangleMeasureHelper? = null
    private var threeCircleMeasureHelper: ThreeCircleMeasureHelper? = null

    fun setImageView(imageView: TpImageView) {
        this.imageView = imageView
    }

    fun setAngleMeasureHelper(helper: AngleMeasureHelper) {
        this.angleMeasureHelper = helper
    }

    fun setFourPointAngleHelper(helper: FourPointAngleHelper) {
        this.fourPointAngleHelper = helper
    }

    fun setPointMeasureHelper(helper: PointMeasureHelper) {
        this.pointMeasureHelper = helper
    }

    fun setLineMeasureHelper(helper: LineMeasureHelper) {
        this.lineMeasureHelper = helper
    }

    fun setHorizonLineMeasureHelper(helper: HorizonLineMeasureHelper) {
        this.horizonLineMeasureHelper = helper
    }

    fun setVerticalLineMeasureHelper(helper: VerticalLineMeasureHelper) {
        this.verticalLineMeasureHelper = helper
    }

    fun setParallelLinesMeasureHelper(helper: ParallelLinesMeasureHelper) {
        this.parallelLinesMeasureHelper = helper
    }

    fun setThreeVerticalMeasureHelper(helper: ThreeVerticalMeasureHelper) {
        this.threeVerticalMeasureHelper = helper
    }

    fun setRectangleMeasureHelper(helper: RectangleMeasureHelper) {
        this.rectangleMeasureHelper = helper
    }

    fun setThreeRectangleMeasureHelper(helper: ThreeRectangleMeasureHelper) {
        this.threeRectangleMeasureHelper = helper
    }

    fun setThreeCircleMeasureHelper(helper: ThreeCircleMeasureHelper) {
        this.threeCircleMeasureHelper = helper
    }

    fun setMeasurementData(data: MeasurementData?) {
        android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] setMeasurementData called with: $data")
        android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] Current overlay size: ${width}x${height}")
        android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] Current visibility: $visibility")

        measurementData = data
        android.util.Log.d("MeasurementOverlay", "📊 Setting measurement data: $data")

        if (data != null) {
            when (data) {
                is AngleMeasurementData -> {
                    android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] AngleMeasurementData - points.size: ${data.points.size}, angle: ${data.angle}°")
                    data.points.forEachIndexed { index, point ->
                        android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] point[$index]: $point")
                    }
                }
                else -> {
                    android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] Other measurement data type: ${data::class.simpleName}")
                }
            }

            // 🎯 确保覆盖层可见并强制重绘
            visibility = View.VISIBLE
            bringToFront() // 确保覆盖层在最前面

            // 强制重绘
            invalidate()
            requestLayout()

            android.util.Log.d("MeasurementOverlay", "✅ Overlay updated and invalidated")
        } else {
            android.util.Log.d("MeasurementOverlay", "🧹 Clearing measurement data")
        }
    }

    fun clearMeasurement() {
        measurementData = null
        allMeasurementData = emptyList()
        allFourPointAngleMeasurementData = emptyList()
        allPointMeasurementData = emptyList()
        allLineMeasurementData = emptyList()
        allHorizonLineMeasurementData = emptyList()
        allVerticalLineMeasurementData = emptyList()
        allParallelLinesMeasurementData = emptyList()
        allThreeVerticalMeasurementData = emptyList()
        allRectangleMeasurementData = emptyList()
        allThreeRectangleMeasurementData = emptyList()
        allEllipseMeasurementData = emptyList()
        allCircleMeasurementData = emptyList()
        allThreeCircleMeasurementData = emptyList()
        allTwoCirclesMeasurementData = emptyList()
        android.util.Log.d("MeasurementOverlay", "🧹 Cleared all measurement data including three vertical, three rectangle, ellipse, circle, three circle, and two circles measurements")
        invalidate()
    }

    /**
     * 🎯 设置多个角度测量数据 - 支持同时显示多个角度
     */
    fun setAllAngleMeasurementData(dataList: List<AngleMeasurementData>) {
        android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] setAllAngleMeasurementData called with ${dataList.size} measurements")

        allMeasurementData = dataList
        measurementData = null // 清除单个测量数据，使用多个测量数据

        dataList.forEachIndexed { index, data ->
            android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] measurement[$index]: points.size=${data.points.size}, angle=${data.angle}°, isDragging=${data.isDragging}")
        }

        if (dataList.isNotEmpty()) {
            // 🎯 确保覆盖层可见并强制重绘
            visibility = View.VISIBLE
            bringToFront()
            invalidate()
            requestLayout()
            android.util.Log.d("MeasurementOverlay", "✅ Multi-angle overlay updated and invalidated")
        } else {
            android.util.Log.d("MeasurementOverlay", "🧹 No angle measurements to display")
        }
    }

    /**
     * 🎯 设置多个四点角度测量数据 - 支持同时显示多个四点角度
     */
    fun setAllFourPointAngleMeasurementData(dataList: List<FourPointAngleMeasurementData>) {
        android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] setAllFourPointAngleMeasurementData called with ${dataList.size} measurements")

        allFourPointAngleMeasurementData = dataList
        measurementData = null // 清除单个测量数据，使用多个测量数据
        allMeasurementData = emptyList() // 清除三点角度数据

        dataList.forEachIndexed { index, data ->
            android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] four-point measurement[$index]: points.size=${data.points.size}, angle=${data.angle}°, isDragging=${data.isDragging}")
        }

        if (dataList.isNotEmpty()) {
            // 🎯 确保覆盖层可见并强制重绘
            visibility = View.VISIBLE
            bringToFront()
            invalidate()
            requestLayout()
            android.util.Log.d("MeasurementOverlay", "✅ Multi four-point angle overlay updated and invalidated")
        } else {
            android.util.Log.d("MeasurementOverlay", "🧹 No four-point angle measurements to display")
        }
    }

    /**
     * 🎯 更新三点角度测量数据 - 混合模式支持
     */
    fun updateAngleMeasurements(dataList: List<AngleMeasurementData>) {
        // android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] updateAngleMeasurements called with ${dataList.size} measurements")
        allMeasurementData = dataList
        invalidate()
    }

    /**
     * 🎯 更新四点角度测量数据 - 混合模式支持
     */
    fun updateFourPointAngleMeasurements(dataList: List<FourPointAngleMeasurementData>) {
        // android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] updateFourPointAngleMeasurements called with ${dataList.size} measurements")
        allFourPointAngleMeasurementData = dataList
        invalidate()
    }

    /**
     * 🎯 更新点测量数据 - 混合模式支持
     */
    fun updatePointMeasurements(dataList: List<PointMeasurementData>) {
        // android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] updatePointMeasurements called with ${dataList.size} measurements")
        allPointMeasurementData = dataList
        invalidate()
    }

    /**
     * 📏 更新线段测量数据 - 混合模式支持
     */
    fun updateLineMeasurements(dataList: List<LineMeasurementData>) {
        // android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] updateLineMeasurements called with ${dataList.size} measurements")
        allLineMeasurementData = dataList
        invalidate()
    }

    /**
     * 📏 更新水平线测量数据 - 混合模式支持
     */
    fun updateHorizonLineMeasurements(dataList: List<HorizonLineMeasurementData>) {
        // android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] updateHorizonLineMeasurements called with ${dataList.size} measurements")
        allHorizonLineMeasurementData = dataList
        invalidate()
    }

    /**
     * 📏 更新垂直线测量数据 - 混合模式支持
     */
    fun updateVerticalLineMeasurements(dataList: List<VerticalLineMeasurementData>) {
        // android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] updateVerticalLineMeasurements called with ${dataList.size} measurements")
        allVerticalLineMeasurementData = dataList
        invalidate()
    }

    /**
     * 📏 更新平行线测量数据 - 混合模式支持
     */
    fun updateParallelLinesMeasurements(dataList: List<ParallelLinesMeasurementData>) {
        // android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] updateParallelLinesMeasurements called with ${dataList.size} measurements")
        allParallelLinesMeasurementData = dataList
        invalidate()
    }

    /**
     * 🎯 设置多个三垂直测量数据 - 支持同时显示多个三垂直测量
     */
    fun setAllThreeVerticalMeasurementData(dataList: List<ThreeVerticalMeasurementData>) {
        android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] setAllThreeVerticalMeasurementData called with ${dataList.size} measurements")

        allThreeVerticalMeasurementData = dataList
        measurementData = null // 清除单个测量数据，使用多个测量数据

        dataList.forEachIndexed { index, data ->
            android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] three vertical measurement[$index]: points.size=${data.viewPoints.size}, distance=${data.distance}px, isSelected=${data.isSelected}")
        }

        // 🎯 确保覆盖层可见并强制重绘
        visibility = View.VISIBLE
        bringToFront()
        invalidate()
        requestLayout()

        if (dataList.isEmpty()) {
            android.util.Log.d("MeasurementOverlay", "🧹 No three vertical measurements to display")
        }
    }

    /**
     * 📏 更新三垂直测量数据 - 混合模式支持
     */
    fun updateThreeVerticalMeasurements(dataList: List<ThreeVerticalMeasurementData>) {
        // android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] updateThreeVerticalMeasurements called with ${dataList.size} measurements")
        allThreeVerticalMeasurementData = dataList
        invalidate()
    }

    /**
     * 📏 更新矩形测量数据 - 混合模式支持
     */
    fun updateRectangleMeasurements(dataList: List<RectangleMeasurementData>) {
        // android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] updateRectangleMeasurements called with ${dataList.size} measurements")
        allRectangleMeasurementData = dataList
        invalidate()
    }

    /**
     * 📏 更新三点矩形测量数据 - 混合模式支持
     */
    fun updateThreeRectangleMeasurements(dataList: List<ThreeRectangleMeasurementData>) {
        // android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] updateThreeRectangleMeasurements called with ${dataList.size} measurements")
        allThreeRectangleMeasurementData = dataList
        invalidate()
    }

    /**
     * 🔵 更新椭圆测量数据 - 混合模式支持
     */
    fun updateEllipseMeasurements(dataList: List<EllipseMeasurement>) {
        // android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] updateEllipseMeasurements called with ${dataList.size} measurements")
        allEllipseMeasurementData = dataList
        invalidate()
    }

    /**
     * ⭕ 更新圆测量数据 - 混合模式支持
     */
    fun updateCircleMeasurements(dataList: List<CenterCircleMeasurement>) {
        // android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] updateCircleMeasurements called with ${dataList.size} measurements")
        allCircleMeasurementData = dataList
        invalidate()
    }

    /**
     * 🎯 更新三点圆测量数据 - 混合模式支持
     */
    fun updateThreeCircleMeasurements(dataList: List<ThreeCircleMeasurement>) {
        android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] updateThreeCircleMeasurements called with ${dataList.size} measurements")
        allThreeCircleMeasurementData = dataList
        invalidate()
    }

    /**
     * ⭕⭕ 更新双圆测量数据 - 混合模式支持
     */
    fun updateTwoCirclesMeasurements(dataList: List<TwoCirclesMeasurement>) {
        android.util.Log.d("MeasurementOverlay", "📊 [DEBUG] updateTwoCirclesMeasurements called with ${dataList.size} measurements")
        allTwoCirclesMeasurementData = dataList
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val imgView = imageView
        if (imgView == null) {
            return
        }

        // 🎯 绘制多个四点角度测量
        if (allFourPointAngleMeasurementData.isNotEmpty()) {
            // android.util.Log.d("MeasurementOverlay", "🎨 Drawing ${allFourPointAngleMeasurementData.size} four-point angle measurements")
            allFourPointAngleMeasurementData.forEach { fourPointAngleData ->
                drawFourPointAngleMeasurement(canvas, fourPointAngleData, imgView)
            }
        }

        // 🎯 绘制多个三点角度测量（与四点角度测量共存）
        if (allMeasurementData.isNotEmpty()) {
            // android.util.Log.d("MeasurementOverlay", "🎨 Drawing ${allMeasurementData.size} angle measurements")
            allMeasurementData.forEach { angleData ->
                drawAngleMeasurement(canvas, angleData, imgView)
            }
        }

        // 🎯 绘制多个点测量（与角度测量共存）
        if (allPointMeasurementData.isNotEmpty()) {
            // android.util.Log.d("MeasurementOverlay", "🎨 Drawing ${allPointMeasurementData.size} point measurements")
            allPointMeasurementData.forEach { pointData ->
                drawPointMeasurement(canvas, pointData, imgView)
            }
        }

        // 📏 绘制多个线段测量（与其他测量共存）
        if (allLineMeasurementData.isNotEmpty()) {
            // android.util.Log.d("MeasurementOverlay", "🎨 Drawing ${allLineMeasurementData.size} line measurements")
            allLineMeasurementData.forEach { lineData ->
                drawLineMeasurement(canvas, lineData, imgView)
            }
        }

        // 📏 绘制多个水平线测量（与其他测量共存）
        if (allHorizonLineMeasurementData.isNotEmpty()) {
            // android.util.Log.d("MeasurementOverlay", "🎨 Drawing ${allHorizonLineMeasurementData.size} horizon line measurements")
            allHorizonLineMeasurementData.forEach { horizonLineData ->
                drawHorizonLineMeasurement(canvas, horizonLineData, imgView)
            }
        }

        // 📏 绘制多个垂直线测量（与其他测量共存）
        if (allVerticalLineMeasurementData.isNotEmpty()) {
            // android.util.Log.d("MeasurementOverlay", "🎨 Drawing ${allVerticalLineMeasurementData.size} vertical line measurements")
            allVerticalLineMeasurementData.forEach { verticalLineData ->
                drawVerticalLineMeasurement(canvas, verticalLineData, imgView)
            }
        }

        // 📏 绘制多个平行线测量（与其他测量共存）
        if (allParallelLinesMeasurementData.isNotEmpty()) {
            // android.util.Log.d("MeasurementOverlay", "🎨 Drawing ${allParallelLinesMeasurementData.size} parallel lines measurements")
            allParallelLinesMeasurementData.forEach { parallelLinesData ->
                drawParallelLinesMeasurement(canvas, parallelLinesData, imgView)
            }
        }

        // 📏 绘制多个三垂直测量（与其他测量共存）
        if (allThreeVerticalMeasurementData.isNotEmpty()) {
            android.util.Log.d("MeasurementOverlay", "🎨 Drawing ${allThreeVerticalMeasurementData.size} three vertical measurements")
            allThreeVerticalMeasurementData.forEach { threeVerticalData ->
                drawThreeVerticalMeasurement(canvas, threeVerticalData, imgView)
            }
        }

        // 📏 绘制多个矩形测量（与其他测量共存）
        if (allRectangleMeasurementData.isNotEmpty()) {
            android.util.Log.d("MeasurementOverlay", "🎨 Drawing ${allRectangleMeasurementData.size} rectangle measurements")
            allRectangleMeasurementData.forEach { rectangleData ->
                drawRectangleMeasurement(canvas, rectangleData, imgView)
            }
        }

        // 📏 绘制多个三点矩形测量（与其他测量共存）
        if (allThreeRectangleMeasurementData.isNotEmpty()) {
            android.util.Log.d("MeasurementOverlay", "🎨 Drawing ${allThreeRectangleMeasurementData.size} three rectangle measurements")
            allThreeRectangleMeasurementData.forEach { threeRectangleData ->
                drawThreeRectangleMeasurement(canvas, threeRectangleData, imgView)
            }
        }

        // 🔵 绘制多个椭圆测量（与其他测量共存）
        if (allEllipseMeasurementData.isNotEmpty()) {
            android.util.Log.d("MeasurementOverlay", "🎨 Drawing ${allEllipseMeasurementData.size} ellipse measurements")
            allEllipseMeasurementData.forEach { ellipseData ->
                drawEllipseMeasurementFromHelper(canvas, ellipseData, imgView)
            }
        }

        // ⭕ 绘制多个圆测量（与其他测量共存）
        if (allCircleMeasurementData.isNotEmpty()) {
            android.util.Log.d("MeasurementOverlay", "🎨 Drawing ${allCircleMeasurementData.size} circle measurements")
            allCircleMeasurementData.forEach { circleData ->
                drawCircleMeasurementFromHelper(canvas, circleData, imgView)
            }
        }

        // 🎯 绘制多个三点圆测量（与其他测量共存）
        if (allThreeCircleMeasurementData.isNotEmpty()) {
            android.util.Log.d("MeasurementOverlay", "🎨 Drawing ${allThreeCircleMeasurementData.size} three circle measurements")
            allThreeCircleMeasurementData.forEach { threeCircleData ->
                drawThreeCircleMeasurement(canvas, threeCircleData, imgView)
            }
        }

        // ⭕⭕ 绘制多个双圆测量（与其他测量共存）
        if (allTwoCirclesMeasurementData.isNotEmpty()) {
            android.util.Log.d("MeasurementOverlay", "🎨 Drawing ${allTwoCirclesMeasurementData.size} two circles measurements")
            allTwoCirclesMeasurementData.forEach { twoCirclesData ->
                drawTwoCirclesMeasurement(canvas, twoCirclesData, imgView)
            }
        }

        // 如果有任何测量数据，则不需要回退到单个测量数据绘制
        if (allFourPointAngleMeasurementData.isNotEmpty() || allMeasurementData.isNotEmpty() || allPointMeasurementData.isNotEmpty() || allLineMeasurementData.isNotEmpty() || allHorizonLineMeasurementData.isNotEmpty() || allVerticalLineMeasurementData.isNotEmpty() || allParallelLinesMeasurementData.isNotEmpty() || allThreeVerticalMeasurementData.isNotEmpty() || allRectangleMeasurementData.isNotEmpty() || allThreeRectangleMeasurementData.isNotEmpty() || allEllipseMeasurementData.isNotEmpty() || allCircleMeasurementData.isNotEmpty() || allThreeCircleMeasurementData.isNotEmpty() || allTwoCirclesMeasurementData.isNotEmpty()) {
            return
        }

        // 🔄 回退到单个测量数据绘制
        val data = measurementData
        if (data == null) {
            return
        }

        when (data) {
            is AngleMeasurementData -> {
                drawAngleMeasurement(canvas, data, imgView)
            }
            is DistanceMeasurementData -> drawDistanceMeasurement(canvas, data, imgView)
            is RectangleMeasurementData -> drawRectangleMeasurement(canvas, data, imgView)
            is CircleMeasurementData -> drawCircleMeasurement(canvas, data, imgView)
            is ThreePointCircleMeasurementData -> drawThreePointCircleMeasurement(canvas, data, imgView)
            is FourPointAngleMeasurementData -> drawFourPointAngleMeasurement(canvas, data, imgView)
            is ParallelLinesMeasurementData -> drawParallelLinesMeasurement(canvas, data, imgView)
            is EllipseMeasurementData -> drawEllipseMeasurement(canvas, data, imgView)
            is MultiPointPathMeasurementData -> drawMultiPointPathMeasurement(canvas, data, imgView)
            is PointMeasurementData -> drawPointMeasurement(canvas, data, imgView)
            is LineMeasurementData -> drawLineMeasurement(canvas, data, imgView)
            is HorizonLineMeasurementData -> drawHorizonLineMeasurement(canvas, data, imgView)
            is VerticalLineMeasurementData -> drawVerticalLineMeasurement(canvas, data, imgView)
            is ThreeVerticalMeasurementData -> drawThreeVerticalMeasurement(canvas, data, imgView)
            is ThreeRectangleMeasurementData -> drawThreeRectangleMeasurement(canvas, data, imgView)
            is TwoCirclesMeasurementData -> {
                // 双圆测量数据的绘制 - 目前主要通过多测量数据绘制，这里作为备用
                android.util.Log.d("MeasurementOverlay", "🎨 Drawing single two circles measurement data")
                // 可以在这里添加单个双圆测量的绘制逻辑，如果需要的话
            }
        }
    }

    /**
     * 🎯 绘制角度测量 - 专业级三点角度可视化
     */
    private fun drawAngleMeasurement(canvas: Canvas, data: AngleMeasurementData, imageView: TpImageView) {
        if (data.points.size < 3) {
            return
        }

        val vertex = data.points[0]    // 顶点
        val point1 = data.points[1]    // 第一个点
        val point2 = data.points[2]    // 第二个点

        // 🎨 根据状态选择绘制样式
        val lineColor = when {
            data.isDragging -> Color.parseColor("#FF5722") // 拖拽时红色
            data.isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
            else -> Color.parseColor("#2196F3") // 默认蓝色
        }

        val currentLinePaint = Paint(linePaint).apply {
            color = lineColor
            strokeWidth = if (data.isSelected || data.isDragging) 8f else 6f
        }

        // 🎨 绘制角度线条
        canvas.drawLine(vertex.x, vertex.y, point1.x, point1.y, currentLinePaint)
        canvas.drawLine(vertex.x, vertex.y, point2.x, point2.y, currentLinePaint)

        // 🎯 绘制角度弧线
        drawAngleArc(canvas, vertex, point1, point2, data.angle, data.isSelected, data.isDragging)

        // 🎨 绘制控制点
        val pointRadius = when {
            data.isDragging -> 24f
            data.isSelected -> 18f
            else -> 14f
        }
        val currentPointPaint = when {
            data.isDragging -> highlightPointPaint
            data.isSelected -> Paint(pointPaint).apply { color = Color.parseColor("#4CAF50") }
            else -> pointPaint
        }

        canvas.drawCircle(vertex.x, vertex.y, pointRadius, currentPointPaint)
        canvas.drawCircle(point1.x, point1.y, pointRadius, currentPointPaint)
        canvas.drawCircle(point2.x, point2.y, pointRadius, currentPointPaint)

        // 🎨 绘制角度文本
        drawAngleText(canvas, vertex, data.angle, data.isDragging, data.isSelected)
    }

    /**
     * 🎨 绘制角度弧线 - 专业级弧线渲染
     */
    private fun drawAngleArc(canvas: Canvas, vertex: PointF, point1: PointF, point2: PointF, angle: Double, isSelected: Boolean = false, isDragging: Boolean = false) {
        val arcRadius = 60f
        
        // 计算两条线的角度
        val angle1 = atan2((point1.y - vertex.y).toDouble(), (point1.x - vertex.x).toDouble())
        val angle2 = atan2((point2.y - vertex.y).toDouble(), (point2.x - vertex.x).toDouble())
        
        var startAngle = Math.toDegrees(angle1).toFloat()
        var sweepAngle = Math.toDegrees(angle2 - angle1).toFloat()
        
        // 确保弧线绘制方向正确
        if (sweepAngle > 180) sweepAngle -= 360
        if (sweepAngle < -180) sweepAngle += 360
        
        val rect = RectF(
            vertex.x - arcRadius,
            vertex.y - arcRadius,
            vertex.x + arcRadius,
            vertex.y + arcRadius
        )
        
        // 🎨 根据状态调整弧线样式
        val currentArcPaint = Paint(arcPaint).apply {
            color = when {
                isDragging -> Color.parseColor("#FF5722") // 拖拽时红色
                isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
                else -> Color.parseColor("#2196F3") // 默认蓝色
            }
            strokeWidth = if (isSelected || isDragging) 6f else 4f
        }

        canvas.drawArc(rect, startAngle, sweepAngle, false, currentArcPaint)
    }

    /**
     * 🎨 绘制角度文本 - 智能位置和专业样式
     */
    private fun drawAngleText(canvas: Canvas, vertex: PointF, angle: Double, isDragging: Boolean, isSelected: Boolean = false) {
        val angleText = String.format("%.1f°", angle)
        val textBounds = Rect()
        textPaint.getTextBounds(angleText, 0, angleText.length, textBounds)

        // 🎯 智能文本位置（在顶点附近）
        val textX = vertex.x + 80f
        val textY = vertex.y - 40f

        // 🎨 绘制文本背景（专业级设计）
        val backgroundPaint = Paint().apply {
            color = when {
                isDragging -> Color.argb(200, 233, 30, 99) // 拖拽时粉色背景
                isSelected -> Color.argb(200, 76, 175, 80) // 选中时绿色背景
                else -> Color.argb(180, 0, 0, 0) // 正常时黑色背景
            }
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        val padding = 12f
        canvas.drawRoundRect(
            textX - padding,
            textY - textBounds.height() - padding,
            textX + textBounds.width() + padding,
            textY + padding,
            12f, 12f,
            backgroundPaint
        )

        // 绘制文本
        canvas.drawText(angleText, textX, textY, textPaint)
    }

    // 其他测量类型的绘制方法将在后续添加
    private fun drawDistanceMeasurement(canvas: Canvas, data: DistanceMeasurementData, imageView: TpImageView) {
        // TODO: 实现距离测量绘制
    }

    private fun drawRectangleMeasurement(canvas: Canvas, data: RectangleMeasurementData, imageView: TpImageView) {
        if (data.viewPoints.size < 4) {
            return
        }

        // 🎨 根据状态选择绘制样式
        val rectangleColor = when {
            data.isDragging -> Color.parseColor("#FF5722") // 拖拽时红色
            data.isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
            else -> Color.parseColor("#2196F3") // 默认蓝色
        }

        // 🎨 矩形边框绘制画笔
        val rectanglePaint = Paint().apply {
            color = rectangleColor
            strokeWidth = if (data.isSelected || data.isDragging) 8f else 6f
            style = Paint.Style.STROKE
            isAntiAlias = true
            strokeCap = Paint.Cap.ROUND
        }

        // 🎨 控制点绘制画笔
        val controlPointPaint = Paint().apply {
            color = rectangleColor
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        val controlPointRadius = if (data.isSelected || data.isDragging) 12f else 10f

        // 📐 绘制矩形边框（四条边）
        val points = data.viewPoints
        // 绘制四条边：左上->右上->右下->左下->左上
        for (i in 0 until 4) {
            val startPoint = points[i]
            val endPoint = points[(i + 1) % 4]
            canvas.drawLine(startPoint.x, startPoint.y, endPoint.x, endPoint.y, rectanglePaint)
        }

        // 🎯 绘制控制点（只在左上角和右下角显示控制点）
        if (data.isSelected || data.isDragging) {
            // 左上角控制点
            canvas.drawCircle(points[0].x, points[0].y, controlPointRadius, controlPointPaint)
            // 右下角控制点
            canvas.drawCircle(points[2].x, points[2].y, controlPointRadius, controlPointPaint)
        }

        // 📏 绘制面积和周长文本
        val centerX = (points[0].x + points[2].x) / 2
        val centerY = (points[0].y + points[2].y) / 2

        val areaText = String.format("面积: %.1f px²", data.area)
        val perimeterText = String.format("周长: %.1f px", data.perimeter)
        val combinedText = "$areaText\n$perimeterText"

        val textPaint = Paint().apply {
            color = Color.WHITE
            textSize = when {
                data.isDragging -> 32f
                data.isSelected -> 30f
                else -> 28f
            }
            isAntiAlias = true
            textAlign = Paint.Align.CENTER
            typeface = Typeface.DEFAULT_BOLD
        }

        val textBackgroundPaint = Paint().apply {
            color = Color.argb(180, 0, 0, 0) // 半透明黑色背景
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 计算文本尺寸
        val textBounds = Rect()
        textPaint.getTextBounds(areaText, 0, areaText.length, textBounds)
        val lineHeight = textBounds.height() + 8f
        val maxTextWidth = maxOf(textPaint.measureText(areaText), textPaint.measureText(perimeterText))

        // 绘制文本背景
        val padding = 12f
        val backgroundRect = RectF(
            centerX - maxTextWidth / 2 - padding,
            centerY - lineHeight - padding,
            centerX + maxTextWidth / 2 + padding,
            centerY + lineHeight + padding
        )
        canvas.drawRoundRect(backgroundRect, 8f, 8f, textBackgroundPaint)

        // 绘制文本
        canvas.drawText(areaText, centerX, centerY - lineHeight / 2, textPaint)
        canvas.drawText(perimeterText, centerX, centerY + lineHeight / 2, textPaint)
    }

    /**
     * 📐 绘制三点矩形测量 - 支持旋转的矩形
     */
    private fun drawThreeRectangleMeasurement(canvas: Canvas, data: ThreeRectangleMeasurementData, imageView: TpImageView) {
        if (data.controlPoints.size < 3 || data.rotatedCorners.size < 4) {
            return
        }

        // 🎨 根据状态选择绘制样式
        val rectangleColor = when {
            data.isDragging -> Color.parseColor("#FF5722") // 拖拽时红色
            data.isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
            else -> Color.parseColor("#2196F3") // 默认蓝色
        }

        // 🎨 矩形边框绘制画笔
        val rectanglePaint = Paint().apply {
            color = rectangleColor
            strokeWidth = if (data.isSelected || data.isDragging) 8f else 6f
            style = Paint.Style.STROKE
            isAntiAlias = true
            strokeCap = Paint.Cap.ROUND
        }

        // 🎨 控制点绘制画笔
        val controlPointPaint = Paint().apply {
            color = rectangleColor
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 🎨 旋转控制点绘制画笔（圆形）
        val rotationControlPaint = Paint().apply {
            color = Color.parseColor("#FF9800") // 橙色表示旋转控制点
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        val controlPointRadius = if (data.isSelected || data.isDragging) 12f else 10f

        // 📐 绘制旋转矩形边框（四条边）
        val corners = data.rotatedCorners
        // 绘制四条边：左上->右上->右下->左下->左上
        for (i in 0 until 4) {
            val startPoint = corners[i]
            val endPoint = corners[(i + 1) % 4]
            canvas.drawLine(startPoint.x, startPoint.y, endPoint.x, endPoint.y, rectanglePaint)
        }

        // 🎯 绘制控制点（三个控制点：左上角、右下角、右上角旋转点）
        if (data.isSelected || data.isDragging) {
            val controlPoints = data.controlPoints
            // 左上角控制点（方形）
            canvas.drawRect(
                controlPoints[0].x - controlPointRadius,
                controlPoints[0].y - controlPointRadius,
                controlPoints[0].x + controlPointRadius,
                controlPoints[0].y + controlPointRadius,
                controlPointPaint
            )
            // 右下角控制点（方形）
            canvas.drawRect(
                controlPoints[2].x - controlPointRadius,
                controlPoints[2].y - controlPointRadius,
                controlPoints[2].x + controlPointRadius,
                controlPoints[2].y + controlPointRadius,
                controlPointPaint
            )
            // 右上角旋转控制点（圆形）
            canvas.drawCircle(controlPoints[1].x, controlPoints[1].y, controlPointRadius, rotationControlPaint)
        }

        // 📏 绘制面积和周长文本
        val centerX = data.centerPoint.x
        val centerY = data.centerPoint.y

        val areaText = String.format("面积: %.1f px²", data.area)
        val perimeterText = String.format("周长: %.1f px", data.perimeter)

        val textPaint = Paint().apply {
            color = Color.WHITE
            textSize = when {
                data.isDragging -> 32f
                data.isSelected -> 30f
                else -> 28f
            }
            isAntiAlias = true
            textAlign = Paint.Align.CENTER
            typeface = Typeface.DEFAULT_BOLD
        }

        val textBackgroundPaint = Paint().apply {
            color = Color.argb(180, 0, 0, 0) // 半透明黑色背景
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 计算文本尺寸
        val textBounds = Rect()
        textPaint.getTextBounds(areaText, 0, areaText.length, textBounds)
        val lineHeight = textBounds.height() + 8f
        val maxTextWidth = maxOf(textPaint.measureText(areaText), textPaint.measureText(perimeterText))

        // 绘制文本背景
        val padding = 12f
        val backgroundRect = RectF(
            centerX - maxTextWidth / 2 - padding,
            centerY - lineHeight - padding,
            centerX + maxTextWidth / 2 + padding,
            centerY + lineHeight + padding
        )
        canvas.drawRoundRect(backgroundRect, 8f, 8f, textBackgroundPaint)

        // 绘制文本
        canvas.drawText(areaText, centerX, centerY - lineHeight / 2, textPaint)
        canvas.drawText(perimeterText, centerX, centerY + lineHeight / 2, textPaint)
    }

    private fun drawCircleMeasurement(canvas: Canvas, data: CircleMeasurementData, imageView: TpImageView) {
        // TODO: 实现圆形测量绘制（旧版本，已被新版本替代）
    }

    private fun drawThreePointCircleMeasurement(canvas: Canvas, data: ThreePointCircleMeasurementData, imageView: TpImageView) {
        // TODO: 实现三点圆测量绘制
    }

    private fun drawFourPointAngleMeasurement(canvas: Canvas, data: FourPointAngleMeasurementData, imageView: TpImageView) {
        if (data.points.size < 4) return

        val p0 = data.points[0] // 第一条线起点
        val p1 = data.points[1] // 第一条线终点
        val p2 = data.points[2] // 第二条线起点
        val p3 = data.points[3] // 第二条线终点

        // 根据状态选择绘制样式
        val lineColor = when {
            data.isDragging -> Color.parseColor("#FF5722") // 拖拽时红色
            data.isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
            else -> Color.parseColor("#2196F3") // 默认蓝色
        }

        val currentLinePaint = Paint(linePaint).apply {
            color = lineColor
            strokeWidth = if (data.isSelected || data.isDragging) 8f else 6f
        }

        // 注意：原始线段将在延长线绘制方法中重新绘制，这里先绘制作为底层
        canvas.drawLine(p0.x, p0.y, p1.x, p1.y, currentLinePaint)
        canvas.drawLine(p2.x, p2.y, p3.x, p3.y, currentLinePaint)

        // 绘制四个控制点
        val pointRadius = when {
            data.isDragging -> 24f
            data.isSelected -> 18f
            else -> 14f
        }
        val currentPointPaint = when {
            data.isDragging -> highlightPointPaint
            data.isSelected -> Paint(pointPaint).apply { color = Color.parseColor("#4CAF50") }
            else -> pointPaint
        }

        // 绘制四个点，使用不同颜色区分
        val pointColors = listOf(
            Color.parseColor("#F44336"),  // 红色点1
            Color.parseColor("#FF9800"),  // 橙色点2
            Color.parseColor("#9C27B0"),  // 紫色点3
            Color.parseColor("#607D8B")   // 蓝灰色点4
        )

        data.points.forEachIndexed { index, point ->
            val pointPaint = Paint(currentPointPaint).apply {
                color = if (data.isSelected || data.isDragging) pointColors[index] else this.color
            }
            canvas.drawCircle(point.x, point.y, pointRadius, pointPaint)
        }

        // 如果有交点，绘制交点和角度弧线
        if (data.isValid && data.intersection != null) {
            // 绘制智能延长线
            drawFourPointExtensionLines(canvas, data.points, data.intersection, data.isSelected, data.isDragging)

            // 绘制交点
            val intersectionPaint = Paint().apply {
                color = Color.parseColor("#E91E63") // 粉色交点
                style = Paint.Style.FILL
                isAntiAlias = true
            }
            canvas.drawCircle(data.intersection.x, data.intersection.y, 8f, intersectionPaint)

            // 绘制角度弧线
            drawFourPointAngleArc(canvas, data.points, data.intersection, data.angle, data.isSelected, data.isDragging)

            // 绘制角度文本
            drawFourPointAngleText(canvas, data.intersection, data.angle, data.isDragging, data.isSelected)
        }
    }

    /**
     * 🎨 绘制四点角度弧线
     */
    private fun drawFourPointAngleArc(canvas: Canvas, points: List<PointF>, intersection: PointF, angle: Double, isSelected: Boolean = false, isDragging: Boolean = false) {
        val arcRadius = 60f

        // 计算两条线的方向向量
        val vec1 = PointF(
            (points[0].x + points[1].x) / 2f - intersection.x,
            (points[0].y + points[1].y) / 2f - intersection.y
        )
        val vec2 = PointF(
            (points[2].x + points[3].x) / 2f - intersection.x,
            (points[2].y + points[3].y) / 2f - intersection.y
        )

        // 计算角度
        val angle1 = atan2(vec1.y.toDouble(), vec1.x.toDouble())
        val angle2 = atan2(vec2.y.toDouble(), vec2.x.toDouble())

        var startAngle = Math.toDegrees(angle1).toFloat()
        var sweepAngle = Math.toDegrees(angle2 - angle1).toFloat()

        // 确保绘制较小的角度
        if (sweepAngle > 180f) {
            sweepAngle -= 360f
        } else if (sweepAngle < -180f) {
            sweepAngle += 360f
        }

        if (sweepAngle < 0) {
            startAngle += sweepAngle
            sweepAngle = -sweepAngle
        }

        val rect = RectF(
            intersection.x - arcRadius,
            intersection.y - arcRadius,
            intersection.x + arcRadius,
            intersection.y + arcRadius
        )

        // 根据状态调整弧线样式
        val currentArcPaint = Paint(arcPaint).apply {
            color = when {
                isDragging -> Color.parseColor("#FF5722") // 拖拽时红色
                isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
                else -> Color.parseColor("#FFEB3B") // 默认黄色
            }
            strokeWidth = if (isSelected || isDragging) 6f else 4f
        }

        canvas.drawArc(rect, startAngle, sweepAngle, false, currentArcPaint)
    }

    /**
     * 🎨 绘制四点角度文本
     */
    private fun drawFourPointAngleText(canvas: Canvas, intersection: PointF, angle: Double, isDragging: Boolean, isSelected: Boolean = false) {
        val angleText = String.format("%.1f°", angle)
        val textBounds = Rect()
        textPaint.getTextBounds(angleText, 0, angleText.length, textBounds)

        // 智能文本位置（在交点附近）
        val textX = intersection.x + 80f
        val textY = intersection.y - 40f

        // 绘制文本背景
        val backgroundPaint = Paint().apply {
            color = when {
                isDragging -> Color.argb(200, 233, 30, 99) // 拖拽时粉色背景
                isSelected -> Color.argb(200, 76, 175, 80) // 选中时绿色背景
                else -> Color.argb(180, 0, 0, 0) // 正常时黑色背景
            }
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        val padding = 12f
        canvas.drawRoundRect(
            textX - padding,
            textY - textBounds.height() - padding,
            textX + textBounds.width() + padding,
            textY + padding,
            12f, 12f,
            backgroundPaint
        )

        // 绘制文本
        canvas.drawText(angleText, textX, textY, textPaint)
    }



    private fun drawEllipseMeasurement(canvas: Canvas, data: EllipseMeasurementData, imageView: TpImageView) {
        // TODO: 实现椭圆测量绘制
    }

    private fun drawMultiPointPathMeasurement(canvas: Canvas, data: MultiPointPathMeasurementData, imageView: TpImageView) {
        // TODO: 实现多点路径测量绘制
    }

    /**
     * 🎯 处理触摸事件 - 委托给测量助手
     */
    override fun onTouchEvent(event: MotionEvent): Boolean {
        android.util.Log.d("MeasurementOverlay", "🎯 Touch event: ${event.action}, forwarding to MeasurementManager")

        // 在混合共存模式下，将所有触摸事件转发给 TpImageView 的 MeasurementManager 处理
        // MeasurementManager 会智能判断是测量操作还是图像拖拽，包括空白区域点击
        forwardEventToImageView(event)
        return false
    }

    /**
     * 🔄 将事件转发给TpImageView
     */
    private fun forwardEventToImageView(event: MotionEvent) {
        imageView?.let { imgView ->
            val adjustedEvent = MotionEvent.obtain(event)
            val handled = imgView.dispatchTouchEvent(adjustedEvent)
            adjustedEvent.recycle()
            android.util.Log.d("MeasurementOverlay", "📱 TpImageView handled event: $handled")
        }
    }

    /**
     * 🎨 绘制四点角度智能延长线
     */
    private fun drawFourPointExtensionLines(canvas: Canvas, points: List<PointF>, intersection: PointF?, isSelected: Boolean, isDragging: Boolean) {
        if (points.size < 4) return

        // 获取智能延长线数据
        val extensions = fourPointAngleHelper?.calculateExtensionLines(points, intersection) ?: return

        // 延长线绘制样式
        val extensionPaint = Paint().apply {
            color = when {
                isDragging -> Color.parseColor("#FF5722") // 拖拽时红色
                isSelected -> Color.parseColor("#757575") // 选中时灰色
                else -> Color.parseColor("#BDBDBD") // 默认浅灰色
            }
            strokeWidth = 2f
            style = Paint.Style.STROKE
            isAntiAlias = true
            pathEffect = DashPathEffect(floatArrayOf(10f, 5f), 0f) // 虚线效果
        }

        // 原始线段绘制样式（用于区分延长部分）
        val originalLinePaint = Paint().apply {
            color = when {
                isDragging -> Color.parseColor("#FF5722") // 拖拽时红色
                isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
                else -> Color.parseColor("#2196F3") // 默认蓝色
            }
            strokeWidth = if (isSelected || isDragging) 8f else 6f
            style = Paint.Style.STROKE
            isAntiAlias = true
        }

        // 绘制智能延长线（只到交点）
        extensions.forEach { (start, end) ->
            canvas.drawLine(start.x, start.y, end.x, end.y, extensionPaint)
            // android.util.Log.d("MeasurementOverlay", "🎨 Drawing extension line: ($start) -> ($end)")
        }

        // 重新绘制原始线段，确保它们在延长线之上
        canvas.drawLine(points[0].x, points[0].y, points[1].x, points[1].y, originalLinePaint)
        canvas.drawLine(points[2].x, points[2].y, points[3].x, points[3].y, originalLinePaint)

        // android.util.Log.d("MeasurementOverlay", "🎨 Drew ${extensions.size} extension lines to intersection")
    }

    /**
     * 🎯 绘制点测量 - 红色圆点+白色十字
     */
    private fun drawPointMeasurement(canvas: Canvas, data: PointMeasurementData, imageView: TpImageView) {
        if (data.points.isEmpty()) {
            return
        }

        val point = data.points[0] // 单个点

        // 🎨 根据状态选择绘制样式
        val pointRadius = when {
            data.isDragging -> 24f
            data.isSelected -> 20f
            else -> 16f
        }

        // 🎨 绘制红色圆点
        val circlePaint = Paint().apply {
            color = Color.parseColor("#FF5722") // Material Design Red 600
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 如果选中，添加绿色边框
        if (data.isSelected) {
            val borderPaint = Paint().apply {
                color = Color.parseColor("#4CAF50") // Material Design Green 500
                style = Paint.Style.STROKE
                strokeWidth = 4f
                isAntiAlias = true
            }
            canvas.drawCircle(point.x, point.y, pointRadius + 2f, borderPaint)
        }

        canvas.drawCircle(point.x, point.y, pointRadius, circlePaint)

        // 🎨 绘制白色十字
        val crossPaint = Paint().apply {
            color = Color.WHITE
            strokeWidth = when {
                data.isDragging -> 4f
                data.isSelected -> 4f
                else -> 3f
            }
            style = Paint.Style.STROKE
            strokeCap = Paint.Cap.ROUND
            isAntiAlias = true
        }

        val crossLength = pointRadius * 0.6f

        // 绘制十字的水平线
        canvas.drawLine(
            point.x - crossLength, point.y,
            point.x + crossLength, point.y,
            crossPaint
        )

        // 绘制十字的垂直线
        canvas.drawLine(
            point.x, point.y - crossLength,
            point.x, point.y + crossLength,
            crossPaint
        )

        // 🎨 如果选中，绘制坐标文本
        if (data.isSelected) {
            val textPaint = Paint().apply {
                color = Color.WHITE
                textSize = 36f
                isAntiAlias = true
                typeface = Typeface.DEFAULT_BOLD
                setShadowLayer(2f, 1f, 1f, Color.BLACK)
            }

            val text = String.format("(%.0f, %.0f)", point.x, point.y)
            val textX = point.x - textPaint.measureText(text) / 2f
            val textY = point.y - pointRadius - 20f

            canvas.drawText(text, textX, textY, textPaint)
        }
    }

    /**
     * 📏 绘制线段测量 - 与AngleMeasureHelper和PointMeasureHelper保持一致的状态视觉反馈
     */
    private fun drawLineMeasurement(canvas: Canvas, data: LineMeasurementData, imageView: TpImageView) {
        if (data.points.size < 2) {
            return
        }

        val startPoint = data.points[0]
        val endPoint = data.points[1]

        // 🎨 根据状态选择绘制样式 - 与AngleMeasureHelper保持一致
        val lineColor = when {
            data.isDragging -> Color.parseColor("#FF5722") // 拖拽时红色
            data.isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
            else -> Color.parseColor("#2196F3") // 默认蓝色
        }

        // 🎨 线段绘制画笔
        val linePaint = Paint().apply {
            color = lineColor
            strokeWidth = if (data.isSelected || data.isDragging) 8f else 6f
            style = Paint.Style.STROKE
            isAntiAlias = true
            strokeCap = Paint.Cap.ROUND
        }

        // 🎨 端点绘制画笔 - 根据状态变化
        val pointRadius = when {
            data.isDragging -> 24f
            data.isSelected -> 20f
            else -> 16f
        }

        val endPointPaint = Paint().apply {
            color = when {
                data.isDragging -> Color.parseColor("#FF5722") // 拖拽时红色
                data.isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
                else -> Color.parseColor("#2196F3") // 默认蓝色
            }
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 🎨 长度文本画笔
        val lengthTextPaint = Paint().apply {
            color = Color.WHITE
            textSize = when {
                data.isDragging -> 40f
                data.isSelected -> 38f
                else -> 36f
            }
            isAntiAlias = true
            textAlign = Paint.Align.CENTER
        }

        // 🎨 文本背景画笔 - 根据状态变化颜色
        val textBackgroundPaint = Paint().apply {
            color = lineColor
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 📏 绘制线段
        canvas.drawLine(startPoint.x, startPoint.y, endPoint.x, endPoint.y, linePaint)

        // 🎯 绘制端点 - 与AngleMeasureHelper保持一致的样式
        canvas.drawCircle(startPoint.x, startPoint.y, pointRadius, endPointPaint)
        canvas.drawCircle(endPoint.x, endPoint.y, pointRadius, endPointPaint)

        // 📏 绘制长度文本
        val midX = (startPoint.x + endPoint.x) / 2
        val midY = (startPoint.y + endPoint.y) / 2 - 30f // 线段中点上方30像素

        val lengthText = String.format("%.1f px", data.length)
        val textWidth = lengthTextPaint.measureText(lengthText)
        val textHeight = lengthTextPaint.textSize

        // 绘制文本背景（圆角矩形）
        val padding = when {
            data.isDragging -> 12f
            data.isSelected -> 10f
            else -> 8f
        }
        val backgroundRect = RectF(
            midX - textWidth / 2 - padding,
            midY - textHeight / 2 - padding,
            midX + textWidth / 2 + padding,
            midY + textHeight / 2 + padding
        )
        canvas.drawRoundRect(backgroundRect, 8f, 8f, textBackgroundPaint)

        // 绘制长度文本
        canvas.drawText(lengthText, midX, midY + textHeight / 4, lengthTextPaint)
    }

    /**
     * 📏 绘制水平线测量 - 专门的水平约束线段绘制
     */
    private fun drawHorizonLineMeasurement(canvas: Canvas, data: HorizonLineMeasurementData, imageView: TpImageView) {
        if (data.viewPoints.size < 2) {
            return
        }

        val leftPoint = data.viewPoints[0]
        val rightPoint = data.viewPoints[1]

        // 🎨 根据状态选择绘制样式
        val lineColor = when {
            data.isEditing -> Color.parseColor("#FF5722") // 编辑时红色
            data.isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
            else -> Color.parseColor("#2196F3") // 默认蓝色
        }

        // 🎨 水平线绘制画笔 - 比普通线段稍粗以突出水平特性
        val linePaint = Paint().apply {
            color = lineColor
            strokeWidth = if (data.isSelected || data.isEditing) 10f else 8f
            style = Paint.Style.STROKE
            isAntiAlias = true
            strokeCap = Paint.Cap.ROUND
        }

        // 🎨 端点绘制画笔 - 左右端点不同样式以区分功能
        val pointRadius = when {
            data.isEditing -> 26f
            data.isSelected -> 22f
            else -> 18f
        }

        // 左端点 - 可自由移动（圆形）
        val leftPointPaint = Paint().apply {
            color = when {
                data.isEditing -> Color.parseColor("#FF5722")
                data.isSelected -> Color.parseColor("#4CAF50")
                else -> Color.parseColor("#2196F3")
            }
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 右端点 - 只能水平移动（方形）
        val rightPointPaint = Paint().apply {
            color = when {
                data.isEditing -> Color.parseColor("#FF5722")
                data.isSelected -> Color.parseColor("#4CAF50")
                else -> Color.parseColor("#2196F3")
            }
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 🎨 水平参考线画笔（虚线）
        val referencePaint = Paint().apply {
            color = Color.parseColor("#80FF5722") // 半透明橙色
            strokeWidth = 2f
            style = Paint.Style.STROKE
            isAntiAlias = true
            pathEffect = DashPathEffect(floatArrayOf(10f, 5f), 0f) // 虚线效果
        }

        // 📏 绘制主水平线
        canvas.drawLine(leftPoint.x, leftPoint.y, rightPoint.x, rightPoint.y, linePaint)

        // 🎯 绘制水平参考线（选中或编辑时显示）
        if (data.isSelected || data.isEditing) {
            val extendLength = 100f // 延长线长度
            canvas.drawLine(
                leftPoint.x - extendLength, data.baselineY,
                rightPoint.x + extendLength, data.baselineY,
                referencePaint
            )
        }

        // 🎯 绘制端点 - 左端点圆形，右端点方形
        canvas.drawCircle(leftPoint.x, leftPoint.y, pointRadius, leftPointPaint)

        // 右端点绘制为方形以区分功能
        val halfSize = pointRadius * 0.8f
        canvas.drawRect(
            rightPoint.x - halfSize, rightPoint.y - halfSize,
            rightPoint.x + halfSize, rightPoint.y + halfSize,
            rightPointPaint
        )

        // 📏 绘制长度文本
        data.textPosition?.let { textPos ->
            val lengthTextPaint = Paint().apply {
                color = Color.WHITE
                textSize = when {
                    data.isEditing -> 42f
                    data.isSelected -> 40f
                    else -> 38f
                }
                isAntiAlias = true
                textAlign = Paint.Align.CENTER
            }

            val textBackgroundPaint = Paint().apply {
                color = lineColor
                style = Paint.Style.FILL
                isAntiAlias = true
            }

            val lengthText = String.format("%.1f px", data.length)
            val textWidth = lengthTextPaint.measureText(lengthText)
            val textHeight = lengthTextPaint.textSize

            // 绘制文本背景
            val padding = when {
                data.isEditing -> 14f
                data.isSelected -> 12f
                else -> 10f
            }
            val backgroundRect = RectF(
                textPos.x - textWidth / 2 - padding,
                textPos.y - textHeight / 2 - padding,
                textPos.x + textWidth / 2 + padding,
                textPos.y + textHeight / 2 + padding
            )
            canvas.drawRoundRect(backgroundRect, 10f, 10f, textBackgroundPaint)

            // 绘制长度文本
            canvas.drawText(lengthText, textPos.x, textPos.y + textHeight / 4, lengthTextPaint)
        }
    }

    /**
     * 📏 绘制垂直线测量 - 专门的垂直约束线段绘制
     */
    private fun drawVerticalLineMeasurement(canvas: Canvas, data: VerticalLineMeasurementData, imageView: TpImageView) {
        if (data.viewPoints.size < 2) {
            return
        }

        val topPoint = data.viewPoints[0]
        val bottomPoint = data.viewPoints[1]

        // 🎨 根据状态选择绘制样式
        val lineColor = when {
            data.isEditing -> Color.parseColor("#FF5722") // 编辑时红色
            data.isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
            else -> Color.parseColor("#2196F3") // 默认蓝色
        }

        // 🎨 垂直线绘制画笔 - 比普通线段稍粗以突出垂直特性
        val linePaint = Paint().apply {
            color = lineColor
            strokeWidth = if (data.isSelected || data.isEditing) 10f else 8f
            style = Paint.Style.STROKE
            isAntiAlias = true
            strokeCap = Paint.Cap.ROUND
        }

        // 🎨 端点绘制画笔 - 上下端点不同样式以区分功能
        val pointRadius = when {
            data.isEditing -> 26f
            data.isSelected -> 22f
            else -> 18f
        }

        // 上端点 - 可自由移动（圆形）
        val topPointPaint = Paint().apply {
            color = when {
                data.isEditing -> Color.parseColor("#FF5722")
                data.isSelected -> Color.parseColor("#4CAF50")
                else -> Color.parseColor("#2196F3")
            }
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 下端点 - 只能垂直移动（方形）
        val bottomPointPaint = Paint().apply {
            color = when {
                data.isEditing -> Color.parseColor("#FF5722")
                data.isSelected -> Color.parseColor("#4CAF50")
                else -> Color.parseColor("#2196F3")
            }
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 🎨 垂直参考线画笔（虚线）
        val referencePaint = Paint().apply {
            color = Color.parseColor("#80FF5722") // 半透明橙色
            strokeWidth = 2f
            style = Paint.Style.STROKE
            isAntiAlias = true
            pathEffect = DashPathEffect(floatArrayOf(10f, 5f), 0f) // 虚线效果
        }

        // 📏 绘制主垂直线
        canvas.drawLine(topPoint.x, topPoint.y, bottomPoint.x, bottomPoint.y, linePaint)

        // 🎯 绘制垂直参考线（选中或编辑时显示）
        if (data.isSelected || data.isEditing) {
            val extendLength = 100f // 延长线长度
            canvas.drawLine(
                data.baselineX, topPoint.y - extendLength,
                data.baselineX, bottomPoint.y + extendLength,
                referencePaint
            )
        }

        // 🎯 绘制端点 - 上端点圆形，下端点方形
        canvas.drawCircle(topPoint.x, topPoint.y, pointRadius, topPointPaint)

        // 下端点绘制为方形以区分功能
        val halfSize = pointRadius * 0.8f
        canvas.drawRect(
            bottomPoint.x - halfSize, bottomPoint.y - halfSize,
            bottomPoint.x + halfSize, bottomPoint.y + halfSize,
            bottomPointPaint
        )

        // 📏 绘制长度文本
        data.textPosition?.let { textPos ->
            val lengthTextPaint = Paint().apply {
                color = Color.WHITE
                textSize = when {
                    data.isEditing -> 42f
                    data.isSelected -> 40f
                    else -> 38f
                }
                isAntiAlias = true
                textAlign = Paint.Align.CENTER
            }

            val textBackgroundPaint = Paint().apply {
                color = lineColor
                style = Paint.Style.FILL
                isAntiAlias = true
            }

            val lengthText = String.format("%.1f px", data.length)
            val textWidth = lengthTextPaint.measureText(lengthText)
            val textHeight = lengthTextPaint.textSize

            // 绘制文本背景
            val padding = when {
                data.isEditing -> 14f
                data.isSelected -> 12f
                else -> 10f
            }
            val backgroundRect = RectF(
                textPos.x - textWidth / 2 - padding,
                textPos.y - textHeight / 2 - padding,
                textPos.x + textWidth / 2 + padding,
                textPos.y + textHeight / 2 + padding
            )
            canvas.drawRoundRect(backgroundRect, 10f, 10f, textBackgroundPaint)

            // 绘制长度文本
            canvas.drawText(lengthText, textPos.x, textPos.y + textHeight / 4, lengthTextPaint)
        }
    }

    /**
     * 📏 绘制平行线测量 - 专业级平行线可视化
     */
    private fun drawParallelLinesMeasurement(canvas: Canvas, data: ParallelLinesMeasurementData, imageView: TpImageView) {
        if (data.viewPoints.size < 4) return

        val line1Start = data.viewPoints[0]
        val line1End = data.viewPoints[1]
        val line2Start = data.viewPoints[2]
        val line2End = data.viewPoints[3]

        // 🎨 线段画笔
        val linePaint = Paint().apply {
            color = when {
                data.isEditing -> Color.parseColor("#FF5722") // 编辑状态：橙色
                data.isSelected -> Color.parseColor("#4CAF50") // 选中状态：绿色
                else -> Color.parseColor("#2196F3") // 默认状态：蓝色
            }
            strokeWidth = when {
                data.isSelected -> 4f
                else -> 3f
            }
            style = Paint.Style.STROKE
            isAntiAlias = true
        }

        // 🎨 端点画笔
        val pointRadius = when {
            data.isSelected -> 12f
            else -> 10f
        }
        val pointPaint = Paint().apply {
            color = when {
                data.isEditing -> Color.parseColor("#FF5722")
                data.isSelected -> Color.parseColor("#4CAF50")
                else -> Color.parseColor("#2196F3")
            }
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 🎨 垂直连接线画笔（虚线）
        val perpendicularPaint = Paint().apply {
            color = Color.parseColor("#80FF5722") // 半透明橙色
            strokeWidth = 2f
            style = Paint.Style.STROKE
            isAntiAlias = true
            pathEffect = DashPathEffect(floatArrayOf(10f, 5f), 0f) // 虚线效果
        }

        // 📏 绘制两条平行线
        canvas.drawLine(line1Start.x, line1Start.y, line1End.x, line1End.y, linePaint)
        canvas.drawLine(line2Start.x, line2Start.y, line2End.x, line2End.y, linePaint)

        // 🎯 绘制垂直连接线（从Line2起点到Line1的垂足）
        val perpFoot = calculatePerpendicularFoot(line2Start, line1Start, line1End)
        canvas.drawLine(line2Start.x, line2Start.y, perpFoot.x, perpFoot.y, perpendicularPaint)

        // 🎯 绘制端点
        canvas.drawCircle(line1Start.x, line1Start.y, pointRadius, pointPaint)
        canvas.drawCircle(line1End.x, line1End.y, pointRadius, pointPaint)
        canvas.drawCircle(line2Start.x, line2Start.y, pointRadius, pointPaint)

        // Line2的右端点用方形表示（自动计算的点）
        val halfSize = pointRadius * 0.8f
        canvas.drawRect(
            line2End.x - halfSize, line2End.y - halfSize,
            line2End.x + halfSize, line2End.y + halfSize,
            pointPaint
        )

        // 📊 绘制距离文本
        val textPosition = data.textPosition ?: run {
            // 如果没有指定文本位置，计算默认位置（垂直连接线中点）
            val midX = (line2Start.x + perpFoot.x) / 2f
            val midY = (line2Start.y + perpFoot.y) / 2f
            PointF(midX, midY)
        }

        val distanceText = String.format("%.1f px", data.distance)

        val textPaint = Paint().apply {
            color = Color.WHITE
            textSize = when {
                data.isSelected -> 16f
                else -> 14f
            }
            isAntiAlias = true
            textAlign = Paint.Align.CENTER
        }

        val textBackgroundPaint = Paint().apply {
            color = when {
                data.isEditing -> Color.parseColor("#CC000000") // 半透明黑色
                data.isSelected -> Color.parseColor("#CC4CAF50") // 半透明绿色
                else -> Color.parseColor("#CC2196F3") // 半透明蓝色
            }
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 计算文本尺寸
        val textBounds = android.graphics.Rect()
        textPaint.getTextBounds(distanceText, 0, distanceText.length, textBounds)
        val textWidth = textBounds.width().toFloat()
        val textHeight = textBounds.height().toFloat()

        // 绘制文本背景
        val padding = when {
            data.isSelected -> 12f
            else -> 10f
        }
        val backgroundRect = RectF(
            textPosition.x - textWidth / 2 - padding,
            textPosition.y - textHeight / 2 - padding,
            textPosition.x + textWidth / 2 + padding,
            textPosition.y + textHeight / 2 + padding
        )
        canvas.drawRoundRect(backgroundRect, 10f, 10f, textBackgroundPaint)

        // 绘制距离文本
        canvas.drawText(distanceText, textPosition.x, textPosition.y + textHeight / 4, textPaint)
    }

    /**
     * 🔧 计算垂足位置
     */
    private fun calculatePerpendicularFoot(point: PointF, lineStart: PointF, lineEnd: PointF): PointF {
        val A = lineEnd.y - lineStart.y
        val B = lineStart.x - lineEnd.x
        val C = lineEnd.x * lineStart.y - lineStart.x * lineEnd.y

        val denominator = A * A + B * B
        if (denominator == 0f) return point // 线段长度为0

        val footX = (B * (B * point.x - A * point.y) - A * C) / denominator
        val footY = (A * (-B * point.x + A * point.y) - B * C) / denominator

        return PointF(footX, footY)
    }

    /**
     * 🎨 绘制三垂直测量 - 专业级渲染
     */
    private fun drawThreeVerticalMeasurement(canvas: Canvas, data: ThreeVerticalMeasurementData, imageView: ImageView) {
        android.util.Log.d("MeasurementOverlay", "🎨 [DEBUG] drawThreeVerticalMeasurement called - points.size=${data.viewPoints.size}, distance=${data.distance}px, isSelected=${data.isSelected}")

        if (data.viewPoints.size < 3) {
            android.util.Log.w("MeasurementOverlay", "⚠️ [DEBUG] Insufficient points for three vertical measurement: ${data.viewPoints.size}")
            return
        }

        val baseLineStart = data.viewPoints[0]  // 基准线起点
        val baseLineEnd = data.viewPoints[1]    // 基准线终点
        val independentPoint = data.viewPoints[2] // 独立点
        val perpendicularFoot = data.perpendicularFoot // 垂足位置

        android.util.Log.d("MeasurementOverlay", "🎨 [DEBUG] Drawing three vertical measurement:")
        android.util.Log.d("MeasurementOverlay", "  baseLineStart: (${baseLineStart.x}, ${baseLineStart.y})")
        android.util.Log.d("MeasurementOverlay", "  baseLineEnd: (${baseLineEnd.x}, ${baseLineEnd.y})")
        android.util.Log.d("MeasurementOverlay", "  independentPoint: (${independentPoint.x}, ${independentPoint.y})")
        android.util.Log.d("MeasurementOverlay", "  perpendicularFoot: ${perpendicularFoot?.let { "(${it.x}, ${it.y})" } ?: "null"}")

        // 🎨 1. 绘制基准线段
        val baseLinePaint = Paint().apply {
            color = when {
                data.isEditing -> Color.RED      // 拖拽时红色
                data.isSelected -> Color.GREEN   // 选中时绿色
                else -> Color.BLUE              // 默认蓝色
            }
            strokeWidth = when {
                data.isSelected -> 6f
                else -> 4f
            }
            style = Paint.Style.STROKE
            isAntiAlias = true
        }

        canvas.drawLine(baseLineStart.x, baseLineStart.y, baseLineEnd.x, baseLineEnd.y, baseLinePaint)
        android.util.Log.d("MeasurementOverlay", "🎨 [DEBUG] Drew baseline from (${baseLineStart.x}, ${baseLineStart.y}) to (${baseLineEnd.x}, ${baseLineEnd.y})")

        // 🎨 2. 绘制垂线段（如果垂足存在）
        if (perpendicularFoot != null) {
            val perpendicularLinePaint = Paint().apply {
                color = Color.parseColor("#FF9800") // 橙色
                strokeWidth = 3f
                style = Paint.Style.STROKE
                isAntiAlias = true
                pathEffect = android.graphics.DashPathEffect(floatArrayOf(10f, 5f), 0f) // 虚线效果
            }

            canvas.drawLine(independentPoint.x, independentPoint.y, perpendicularFoot.x, perpendicularFoot.y, perpendicularLinePaint)
            android.util.Log.d("MeasurementOverlay", "🎨 [DEBUG] Drew perpendicular line from (${independentPoint.x}, ${independentPoint.y}) to (${perpendicularFoot.x}, ${perpendicularFoot.y})")

            // 🎨 3. 绘制垂足点（小圆点）
            val footPointPaint = Paint().apply {
                color = Color.parseColor("#FF9800") // 橙色
                style = Paint.Style.FILL
                isAntiAlias = true
            }

            canvas.drawCircle(perpendicularFoot.x, perpendicularFoot.y, 6f, footPointPaint)
            android.util.Log.d("MeasurementOverlay", "🎨 [DEBUG] Drew perpendicular foot at (${perpendicularFoot.x}, ${perpendicularFoot.y})")
        }

        // 🎨 4. 绘制基准线端点
        val endPointPaint = Paint().apply {
            color = when {
                data.isEditing -> Color.RED
                data.isSelected -> Color.GREEN
                else -> Color.BLUE
            }
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        val pointRadius = when {
            data.isSelected -> 12f
            else -> 10f
        }

        canvas.drawCircle(baseLineStart.x, baseLineStart.y, pointRadius, endPointPaint)
        canvas.drawCircle(baseLineEnd.x, baseLineEnd.y, pointRadius, endPointPaint)
        android.util.Log.d("MeasurementOverlay", "🎨 [DEBUG] Drew baseline endpoints")

        // 🎨 5. 绘制独立点（红色圆点）
        val independentPointPaint = Paint().apply {
            color = Color.RED
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        canvas.drawCircle(independentPoint.x, independentPoint.y, pointRadius, independentPointPaint)
        android.util.Log.d("MeasurementOverlay", "🎨 [DEBUG] Drew independent point at (${independentPoint.x}, ${independentPoint.y})")

        // 🎨 6. 绘制距离标注
        val textPosition = data.textPosition ?: run {
            // 如果没有指定文本位置，计算默认位置（垂线段中点偏移）
            if (perpendicularFoot != null) {
                val midX = (independentPoint.x + perpendicularFoot.x) / 2f + 20f
                val midY = (independentPoint.y + perpendicularFoot.y) / 2f - 20f
                PointF(midX, midY)
            } else {
                PointF(independentPoint.x + 20f, independentPoint.y - 20f)
            }
        }

        val distanceText = String.format("%.1f px", data.distance)

        val textPaint = Paint().apply {
            color = Color.WHITE
            textSize = when {
                data.isSelected -> 16f
                else -> 14f
            }
            isAntiAlias = true
            textAlign = Paint.Align.CENTER
        }

        val textBackgroundPaint = Paint().apply {
            color = when {
                data.isEditing -> Color.parseColor("#CC000000") // 半透明黑色
                data.isSelected -> Color.parseColor("#CC4CAF50") // 半透明绿色
                else -> Color.parseColor("#CCFF9800") // 半透明橙色
            }
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 计算文本尺寸
        val textBounds = android.graphics.Rect()
        textPaint.getTextBounds(distanceText, 0, distanceText.length, textBounds)
        val textWidth = textBounds.width().toFloat()
        val textHeight = textBounds.height().toFloat()

        // 绘制文本背景
        val padding = when {
            data.isSelected -> 12f
            else -> 8f
        }

        canvas.drawRoundRect(
            textPosition.x - textWidth / 2f - padding,
            textPosition.y - textHeight / 2f - padding,
            textPosition.x + textWidth / 2f + padding,
            textPosition.y + textHeight / 2f + padding,
            8f, 8f,
            textBackgroundPaint
        )

        // 绘制文本
        canvas.drawText(distanceText, textPosition.x, textPosition.y + textHeight / 4f, textPaint)

        android.util.Log.d("MeasurementOverlay", "🎨 [DEBUG] Drew distance text '$distanceText' at (${textPosition.x}, ${textPosition.y})")
        android.util.Log.d("MeasurementOverlay", "✅ [DEBUG] Three vertical measurement drawing completed")
    }

    /**
     * 📐 绘制矩形测量（从RectangleMeasureHelper获取的数据）
     */
    private fun drawRectangleMeasurementFromHelper(canvas: Canvas, data: RectangleMeasurement, imageView: TpImageView) {
        // 🎨 根据状态选择绘制样式
        val rectangleColor = when {
            data.isEditing -> Color.parseColor("#FF5722") // 编辑时红色
            data.isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
            else -> Color.parseColor("#2196F3") // 默认蓝色
        }

        // 🎨 矩形边框绘制画笔
        val rectanglePaint = Paint().apply {
            color = rectangleColor
            strokeWidth = if (data.isSelected || data.isEditing) 8f else 6f
            style = Paint.Style.STROKE
            isAntiAlias = true
            strokeCap = Paint.Cap.ROUND
        }

        // 🎨 控制点绘制画笔
        val controlPointPaint = Paint().apply {
            color = rectangleColor
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        val controlPointRadius = if (data.isSelected || data.isEditing) 12f else 10f

        // 📐 绘制矩形边框（四条边）
        val viewPoints = data.viewPoints
        // 绘制四条边：左上->右上->右下->左下->左上
        for (i in 0 until 4) {
            val startPoint = viewPoints[i]
            val endPoint = viewPoints[(i + 1) % 4]
            canvas.drawLine(startPoint.x, startPoint.y, endPoint.x, endPoint.y, rectanglePaint)
        }

        // 🎯 绘制控制点（只在左上角和右下角显示控制点）
        if (data.isSelected || data.isEditing) {
            // 左上角控制点
            canvas.drawCircle(viewPoints[0].x, viewPoints[0].y, controlPointRadius, controlPointPaint)
            // 右下角控制点
            canvas.drawCircle(viewPoints[2].x, viewPoints[2].y, controlPointRadius, controlPointPaint)
        }

        // 📏 绘制面积和周长文本
        val centerX = (viewPoints[0].x + viewPoints[2].x) / 2
        val centerY = (viewPoints[0].y + viewPoints[2].y) / 2

        val areaText = String.format("面积: %.1f px²", data.calculateArea())
        val perimeterText = String.format("周长: %.1f px", data.calculatePerimeter())

        val textPaint = Paint().apply {
            color = Color.WHITE
            textSize = when {
                data.isEditing -> 32f
                data.isSelected -> 30f
                else -> 28f
            }
            isAntiAlias = true
            textAlign = Paint.Align.CENTER
            typeface = Typeface.DEFAULT_BOLD
        }

        val textBackgroundPaint = Paint().apply {
            color = Color.argb(180, 0, 0, 0) // 半透明黑色背景
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 计算文本尺寸
        val textBounds = Rect()
        textPaint.getTextBounds(areaText, 0, areaText.length, textBounds)
        val lineHeight = textBounds.height() + 8f
        val maxTextWidth = maxOf(textPaint.measureText(areaText), textPaint.measureText(perimeterText))

        // 绘制文本背景
        val padding = 12f
        val backgroundRect = RectF(
            centerX - maxTextWidth / 2 - padding,
            centerY - lineHeight - padding,
            centerX + maxTextWidth / 2 + padding,
            centerY + lineHeight + padding
        )
        canvas.drawRoundRect(backgroundRect, 8f, 8f, textBackgroundPaint)

        // 绘制文本
        canvas.drawText(areaText, centerX, centerY - lineHeight / 2, textPaint)
        canvas.drawText(perimeterText, centerX, centerY + lineHeight / 2, textPaint)
    }

    /**
     * 🔵 绘制椭圆测量（从EllipseMeasureHelper获取的数据）
     */
    private fun drawEllipseMeasurementFromHelper(canvas: Canvas, data: EllipseMeasurement, imageView: TpImageView) {
        if (data.viewPoints.size < 3) {
            return
        }

        // 🎨 根据状态选择绘制样式
        val ellipseColor = when {
            data.isEditing -> Color.parseColor("#FF5722") // 编辑时红色
            data.isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
            else -> Color.parseColor("#9C27B0") // 默认紫色
        }

        // 🎨 椭圆边框绘制画笔
        val ellipsePaint = Paint().apply {
            color = ellipseColor
            strokeWidth = if (data.isSelected || data.isEditing) 8f else 6f
            style = Paint.Style.STROKE
            isAntiAlias = true
            strokeCap = Paint.Cap.ROUND
        }

        // 🎨 控制点绘制画笔
        val controlPointPaint = Paint().apply {
            color = ellipseColor
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        val controlPointRadius = if (data.isSelected || data.isEditing) 12f else 10f

        // 📐 获取椭圆参数
        val center = data.viewPoints[0]
        val majorEnd = data.viewPoints[1]
        val minorEnd = data.viewPoints[2]

        // 计算椭圆参数
        val semiMajorAxis = sqrt((center.x - majorEnd.x).pow(2) + (center.y - majorEnd.y).pow(2))
        val semiMinorAxis = sqrt((center.x - minorEnd.x).pow(2) + (center.y - minorEnd.y).pow(2))
        val rotation = atan2(majorEnd.y - center.y, majorEnd.x - center.x)

        // 🔵 绘制椭圆
        val path = Path()
        val matrix = Matrix()

        // 创建标准椭圆路径
        val rect = RectF(
            center.x - semiMajorAxis,
            center.y - semiMinorAxis,
            center.x + semiMajorAxis,
            center.y + semiMinorAxis
        )
        path.addOval(rect, Path.Direction.CW)

        // 应用旋转变换
        matrix.setRotate(Math.toDegrees(rotation.toDouble()).toFloat(), center.x, center.y)
        path.transform(matrix)

        // 绘制椭圆路径
        canvas.drawPath(path, ellipsePaint)

        // 🎯 绘制控制点（中心点、长轴端点、短轴端点）
        if (data.isSelected || data.isEditing) {
            // 中心点
            canvas.drawCircle(center.x, center.y, controlPointRadius, controlPointPaint)
            // 长轴端点
            canvas.drawCircle(majorEnd.x, majorEnd.y, controlPointRadius, controlPointPaint)
            // 短轴端点
            canvas.drawCircle(minorEnd.x, minorEnd.y, controlPointRadius, controlPointPaint)
        }

        // 📏 绘制面积和周长文本
        val areaText = String.format("面积: %.1f px²", data.calculateArea())
        val perimeterText = String.format("周长: %.1f px", data.calculatePerimeter())

        val textPaint = Paint().apply {
            color = Color.WHITE
            textSize = when {
                data.isEditing -> 24f  // 减小字体：32f -> 24f
                data.isSelected -> 22f  // 减小字体：30f -> 22f
                else -> 20f  // 减小字体：28f -> 20f
            }
            isAntiAlias = true
            textAlign = Paint.Align.CENTER
            typeface = Typeface.DEFAULT_BOLD
        }

        val textBackgroundPaint = Paint().apply {
            color = Color.argb(180, 0, 0, 0) // 半透明黑色背景
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 计算文本尺寸
        val textBounds = Rect()
        textPaint.getTextBounds(areaText, 0, areaText.length, textBounds)
        val lineHeight = textBounds.height() + 8f
        val maxTextWidth = maxOf(textPaint.measureText(areaText), textPaint.measureText(perimeterText))

        // 🔵 使用新的文本位置（圆心下方）
        val textPosition = data.calculateTextPosition()

        // 绘制文本背景
        val padding = 12f
        val backgroundRect = RectF(
            textPosition.x - maxTextWidth / 2 - padding,
            textPosition.y - lineHeight - padding,
            textPosition.x + maxTextWidth / 2 + padding,
            textPosition.y + lineHeight + padding
        )
        canvas.drawRoundRect(backgroundRect, 8f, 8f, textBackgroundPaint)

        // 绘制文本
        canvas.drawText(areaText, textPosition.x, textPosition.y - lineHeight / 2, textPaint)
        canvas.drawText(perimeterText, textPosition.x, textPosition.y + lineHeight / 2, textPaint)
    }

    /**
     * ⭕ 绘制圆测量（从CenterCircleMeasureHelper获取的数据）
     */
    private fun drawCircleMeasurementFromHelper(canvas: Canvas, data: CenterCircleMeasurement, imageView: TpImageView) {
        if (data.viewPoints.size < 2) {
            return
        }

        // 🎨 根据状态选择绘制样式
        val circleColor = when {
            data.isEditing -> Color.parseColor("#FF5722") // 编辑时红色
            data.isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
            else -> Color.parseColor("#FF9800") // 默认橙色
        }

        // 🎨 圆形边框绘制画笔
        val circlePaint = Paint().apply {
            color = circleColor
            strokeWidth = if (data.isSelected || data.isEditing) 8f else 6f
            style = Paint.Style.STROKE
            isAntiAlias = true
            strokeCap = Paint.Cap.ROUND
        }

        // 🎨 控制点绘制画笔
        val controlPointPaint = Paint().apply {
            color = circleColor
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        val controlPointRadius = if (data.isSelected || data.isEditing) 12f else 10f

        // 📐 获取圆参数
        val center = data.viewPoints[0]
        val edgePoint = data.viewPoints[1]

        // 🔧 使用专用的视图半径计算方法，确保圆和控制点一致
        val viewRadius = data.calculateViewRadius()

        // 🐛 调试信息：输出坐标和半径信息
        android.util.Log.d("CircleDebug", "🎯 Drawing circle - Center: (${center.x}, ${center.y}), Edge: (${edgePoint.x}, ${edgePoint.y}), ViewRadius: $viewRadius")
        android.util.Log.d("CircleDebug", "🎯 Bitmap coords - Center: (${data.bitmapPoints.getOrNull(0)}), Edge: (${data.bitmapPoints.getOrNull(1)})")
        android.util.Log.d("CircleDebug", "🎯 Calculated radius from bitmap: ${data.calculateRadius()}")

        // ⭕ 绘制圆形
        canvas.drawCircle(center.x, center.y, viewRadius, circlePaint)

        // 🎯 绘制控制点（中心点和边缘点）
        if (data.isSelected || data.isEditing) {
            // 中心点 - 使用不同颜色标识
            val centerPointPaint = Paint(controlPointPaint).apply {
                color = Color.parseColor("#E91E63") // 粉红色中心点
            }
            canvas.drawCircle(center.x, center.y, controlPointRadius, centerPointPaint)

            // 边缘点
            canvas.drawCircle(edgePoint.x, edgePoint.y, controlPointRadius, controlPointPaint)

            // 绘制从中心到边缘的半径线
            val radiusLinePaint = Paint().apply {
                color = circleColor
                strokeWidth = 3f
                style = Paint.Style.STROKE
                isAntiAlias = true
                pathEffect = DashPathEffect(floatArrayOf(10f, 5f), 0f) // 虚线效果
            }
            canvas.drawLine(center.x, center.y, edgePoint.x, edgePoint.y, radiusLinePaint)
        }

        // 📏 绘制面积和周长文本
        val areaText = String.format("面积: %.1f px²", data.calculateArea())
        val circumferenceText = String.format("周长: %.1f px", data.calculateCircumference())

        val textPaint = Paint().apply {
            color = Color.WHITE
            textSize = when {
                data.isEditing -> 24f
                data.isSelected -> 22f
                else -> 20f
            }
            isAntiAlias = true
            textAlign = Paint.Align.CENTER
            typeface = Typeface.DEFAULT_BOLD
        }

        val textBackgroundPaint = Paint().apply {
            color = Color.argb(180, 0, 0, 0) // 半透明黑色背景
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 计算文本尺寸
        val textBounds = Rect()
        textPaint.getTextBounds(areaText, 0, areaText.length, textBounds)
        val lineHeight = textBounds.height() + 8f
        val maxTextWidth = maxOf(textPaint.measureText(areaText), textPaint.measureText(circumferenceText))

        // ⭕ 使用新的文本位置（圆心下方）
        val textPosition = data.calculateTextPosition()

        // 绘制文本背景
        val padding = 12f
        val backgroundRect = RectF(
            textPosition.x - maxTextWidth / 2 - padding,
            textPosition.y - lineHeight - padding,
            textPosition.x + maxTextWidth / 2 + padding,
            textPosition.y + lineHeight + padding
        )
        canvas.drawRoundRect(backgroundRect, 8f, 8f, textBackgroundPaint)

        // 绘制文本
        canvas.drawText(areaText, textPosition.x, textPosition.y - lineHeight / 2, textPaint)
        canvas.drawText(circumferenceText, textPosition.x, textPosition.y + lineHeight / 2, textPaint)
    }

    /**
     * 🎯 绘制三点圆测量（从ThreeCircleMeasureHelper获取的数据）
     */
    private fun drawThreeCircleMeasurement(canvas: Canvas, data: ThreeCircleMeasurement, imageView: TpImageView) {
        if (data.viewPoints.size < 3) {
            return
        }

        // 🎨 根据状态选择绘制样式
        val circleColor = when {
            data.isEditing -> Color.parseColor("#FF5722") // 编辑时红色
            data.isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
            else -> Color.parseColor("#FF9800") // 默认橙色
        }

        // 🎨 圆形边框绘制画笔
        val circlePaint = Paint().apply {
            color = circleColor
            strokeWidth = if (data.isSelected || data.isEditing) 8f else 6f
            style = Paint.Style.STROKE
            isAntiAlias = true
            strokeCap = Paint.Cap.ROUND
        }

        // 🎨 控制点绘制画笔
        val controlPointPaint = Paint().apply {
            color = circleColor
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        val controlPointRadius = if (data.isSelected || data.isEditing) 12f else 10f

        // 📐 计算圆心和半径
        val (center, radius, isValid) = data.calculateCircleFromThreePoints()

        if (!isValid || radius <= 0) {
            // 如果无法计算有效圆，只绘制三个控制点
            for (i in 0 until minOf(3, data.viewPoints.size)) {
                val point = data.viewPoints[i]
                canvas.drawCircle(point.x, point.y, controlPointRadius, controlPointPaint)
            }
            return
        }

        android.util.Log.d("ThreeCircleDebug", "🎯 Drawing three-point circle - Center: (${center.x}, ${center.y}), Radius: $radius")

        // ⭕ 绘制圆形
        canvas.drawCircle(center.x, center.y, radius.toFloat(), circlePaint)

        // 🎯 绘制控制点
        if (data.isSelected || data.isEditing) {
            // 绘制三个边缘控制点
            for (i in 0 until minOf(3, data.viewPoints.size)) {
                val point = data.viewPoints[i]
                canvas.drawCircle(point.x, point.y, controlPointRadius, controlPointPaint)

                // 绘制从圆心到控制点的连线
                val radiusLinePaint = Paint().apply {
                    color = circleColor
                    strokeWidth = 2f
                    style = Paint.Style.STROKE
                    isAntiAlias = true
                    pathEffect = DashPathEffect(floatArrayOf(8f, 4f), 0f) // 虚线效果
                }
                canvas.drawLine(center.x, center.y, point.x, point.y, radiusLinePaint)
            }

            // 绘制圆心标记（如果存在）
            if (data.viewPoints.size >= 4) {
                val centerPointPaint = Paint(controlPointPaint).apply {
                    color = Color.parseColor("#E91E63") // 粉红色圆心点
                }
                canvas.drawCircle(center.x, center.y, controlPointRadius * 0.8f, centerPointPaint)
            }
        }

        // 📏 绘制测量数据文本
        val radiusText = String.format("半径: %.1f px", radius)
        val areaText = String.format("面积: %.1f px²", data.calculateArea())
        val perimeterText = String.format("周长: %.1f px", data.calculatePerimeter())

        val textPaint = Paint().apply {
            color = Color.WHITE
            textSize = when {
                data.isEditing -> 24f
                data.isSelected -> 22f
                else -> 20f
            }
            isAntiAlias = true
            textAlign = Paint.Align.CENTER
            typeface = Typeface.DEFAULT_BOLD
        }

        val textBackgroundPaint = Paint().apply {
            color = Color.argb(180, 0, 0, 0) // 半透明黑色背景
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 计算文本尺寸
        val textBounds = Rect()
        textPaint.getTextBounds(radiusText, 0, radiusText.length, textBounds)
        val lineHeight = textBounds.height() + 6f
        val maxTextWidth = maxOf(
            textPaint.measureText(radiusText),
            textPaint.measureText(areaText),
            textPaint.measureText(perimeterText)
        )

        // 使用圆心作为文本位置
        val textPosition = data.calculateTextPosition()

        // 绘制文本背景
        val padding = 10f
        val backgroundRect = RectF(
            textPosition.x - maxTextWidth / 2 - padding,
            textPosition.y - lineHeight * 1.5f - padding,
            textPosition.x + maxTextWidth / 2 + padding,
            textPosition.y + lineHeight * 1.5f + padding
        )
        canvas.drawRoundRect(backgroundRect, 6f, 6f, textBackgroundPaint)

        // 绘制三行文本
        canvas.drawText(radiusText, textPosition.x, textPosition.y - lineHeight, textPaint)
        canvas.drawText(areaText, textPosition.x, textPosition.y, textPaint)
        canvas.drawText(perimeterText, textPosition.x, textPosition.y + lineHeight, textPaint)
    }

    /**
     * ⭕⭕ 绘制双圆测量（从TwoCirclesMeasureHelper获取的数据）
     */
    private fun drawTwoCirclesMeasurement(canvas: Canvas, data: TwoCirclesMeasurement, imageView: TpImageView) {
        if (data.viewPoints.size < 3) {
            android.util.Log.w("TwoCirclesOverlay", "⚠️ Insufficient points for two circles measurement: ${data.viewPoints.size}")
            return
        }

        // 🎨 根据状态选择绘制样式
        val baseColor = when {
            data.isEditing -> Color.parseColor("#FF5722") // 编辑时红色
            data.isSelected -> Color.parseColor("#4CAF50") // 选中时绿色
            else -> Color.parseColor("#FF9800") // 默认橙色
        }

        // 🎨 双圆边框绘制画笔
        val circlePaint = Paint().apply {
            color = baseColor
            strokeWidth = if (data.isSelected || data.isEditing) 8f else 6f
            style = Paint.Style.STROKE
            isAntiAlias = true
            strokeCap = Paint.Cap.ROUND
        }

        // 🎨 控制点绘制画笔
        val controlPointRadius = if (data.isSelected || data.isEditing) 12f else 10f

        // 📐 获取双圆参数
        val center = data.viewPoints[0]           // 圆心
        val innerEdgePoint = data.viewPoints[1]   // 内圆半径点
        val outerEdgePoint = data.viewPoints[2]   // 外圆半径点

        // 🔧 使用专用的视图半径计算方法
        val innerViewRadius = data.calculateInnerViewRadius()
        val outerViewRadius = data.calculateOuterViewRadius()

        // 🐛 调试信息
        android.util.Log.d("TwoCirclesDebug", "🎯 Drawing two circles - Center: (${center.x}, ${center.y})")
        android.util.Log.d("TwoCirclesDebug", "🎯 Inner: Edge(${innerEdgePoint.x}, ${innerEdgePoint.y}), Radius: $innerViewRadius")
        android.util.Log.d("TwoCirclesDebug", "🎯 Outer: Edge(${outerEdgePoint.x}, ${outerEdgePoint.y}), Radius: $outerViewRadius")

        // ⭕ 绘制外圆
        canvas.drawCircle(center.x, center.y, outerViewRadius, circlePaint)

        // ⭕ 绘制内圆
        canvas.drawCircle(center.x, center.y, innerViewRadius, circlePaint)

        // 🎯 绘制控制点（仅在选中或编辑状态下）
        if (data.isSelected || data.isEditing) {
            // 圆心点 - 粉红色
            val centerPointPaint = Paint().apply {
                color = Color.parseColor("#E91E63") // 粉红色中心点
                style = Paint.Style.FILL
                isAntiAlias = true
            }
            canvas.drawCircle(center.x, center.y, controlPointRadius, centerPointPaint)

            // 内圆半径点 - 蓝色
            val innerPointPaint = Paint().apply {
                color = Color.parseColor("#2196F3") // 蓝色内圆点
                style = Paint.Style.FILL
                isAntiAlias = true
            }
            canvas.drawCircle(innerEdgePoint.x, innerEdgePoint.y, controlPointRadius, innerPointPaint)

            // 外圆半径点 - 绿色
            val outerPointPaint = Paint().apply {
                color = Color.parseColor("#4CAF50") // 绿色外圆点
                style = Paint.Style.FILL
                isAntiAlias = true
            }
            canvas.drawCircle(outerEdgePoint.x, outerEdgePoint.y, controlPointRadius, outerPointPaint)

            // 绘制半径线（虚线效果）
            val radiusLinePaint = Paint().apply {
                strokeWidth = 3f
                style = Paint.Style.STROKE
                isAntiAlias = true
                pathEffect = DashPathEffect(floatArrayOf(10f, 5f), 0f) // 虚线效果
            }

            // 内圆半径线 - 蓝色
            radiusLinePaint.color = Color.parseColor("#2196F3")
            canvas.drawLine(center.x, center.y, innerEdgePoint.x, innerEdgePoint.y, radiusLinePaint)

            // 外圆半径线 - 绿色
            radiusLinePaint.color = Color.parseColor("#4CAF50")
            canvas.drawLine(center.x, center.y, outerEdgePoint.x, outerEdgePoint.y, radiusLinePaint)
        }

        // 📏 绘制测量数据文本
        val displayText = data.getDisplayText()

        val textPaint = Paint().apply {
            color = Color.WHITE
            textSize = when {
                data.isEditing -> 24f
                data.isSelected -> 22f
                else -> 20f
            }
            isAntiAlias = true
            textAlign = Paint.Align.CENTER
            typeface = Typeface.DEFAULT_BOLD
        }

        val textBackgroundPaint = Paint().apply {
            color = Color.argb(180, 0, 0, 0) // 半透明黑色背景
            style = Paint.Style.FILL
            isAntiAlias = true
        }

        // 分割文本为多行
        val textLines = displayText.split("\n")
        val lineHeight = textPaint.textSize + 8f
        val maxTextWidth = textLines.maxOfOrNull { textPaint.measureText(it) } ?: 0f

        // ⭕ 使用计算的文本位置（外圆下方）
        val textPosition = data.calculateTextPosition()

        // 绘制文本背景
        val padding = 12f
        val totalTextHeight = textLines.size * lineHeight
        val backgroundRect = RectF(
            textPosition.x - maxTextWidth / 2 - padding,
            textPosition.y - totalTextHeight / 2 - padding,
            textPosition.x + maxTextWidth / 2 + padding,
            textPosition.y + totalTextHeight / 2 + padding
        )
        canvas.drawRoundRect(backgroundRect, 8f, 8f, textBackgroundPaint)

        // 绘制多行文本
        textLines.forEachIndexed { index, line ->
            val yOffset = textPosition.y - totalTextHeight / 2 + (index + 0.5f) * lineHeight
            canvas.drawText(line, textPosition.x, yOffset, textPaint)
        }

        android.util.Log.d("TwoCirclesOverlay", "✅ Drew two circles measurement with ${textLines.size} text lines")
    }

    // onInterceptTouchEvent方法已移除，因为View类不支持此方法（仅ViewGroup支持）
}
