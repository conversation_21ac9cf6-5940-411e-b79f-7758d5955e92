<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="popup_menu_layout" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\popup_menu_layout.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/popup_menu_layout_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="63" endOffset="14"/></Target><Target id="@+id/btn_scene" view="ImageButton"><Expressions/><location startLine="8" startOffset="4" endLine="14" endOffset="40"/></Target><Target id="@+id/btn_exposure" view="ImageButton"><Expressions/><location startLine="16" startOffset="4" endLine="22" endOffset="40"/></Target><Target id="@+id/btn_white_balance" view="ImageButton"><Expressions/><location startLine="24" startOffset="4" endLine="30" endOffset="40"/></Target><Target id="@+id/btn_color_adjustment" view="ImageButton"><Expressions/><location startLine="32" startOffset="4" endLine="38" endOffset="40"/></Target><Target id="@+id/btn_image_processing" view="ImageButton"><Expressions/><location startLine="40" startOffset="4" endLine="46" endOffset="40"/></Target><Target id="@+id/btn_flip" view="ImageButton"><Expressions/><location startLine="48" startOffset="4" endLine="54" endOffset="40"/></Target><Target id="@+id/btn_power_frequency" view="ImageButton"><Expressions/><location startLine="56" startOffset="4" endLine="61" endOffset="54"/></Target></Targets></Layout>