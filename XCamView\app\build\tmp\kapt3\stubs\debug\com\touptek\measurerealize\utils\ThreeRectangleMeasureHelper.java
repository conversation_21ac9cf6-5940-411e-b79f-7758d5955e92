package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 🎯 专业级三点矩形测量助手 - 遵循现有架构模式
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000n\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0011\u0018\u0000 82\u00020\u0001:\u00018B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0018\u001a\u00020\u0019J\u0006\u0010\u001a\u001a\u00020\u0011J\u0006\u0010\u001b\u001a\u00020\u0011J\u0006\u0010\u001c\u001a\u00020\bJ\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u001f0\u001eJ\u0006\u0010 \u001a\u00020\u0004J \u0010!\u001a\u00020\b2\u0006\u0010\"\u001a\u00020#2\u0006\u0010$\u001a\u00020\u00042\u0006\u0010%\u001a\u00020\u0004H\u0002J\u001e\u0010&\u001a\u00020\b2\u0006\u0010\'\u001a\u00020(2\u0006\u0010$\u001a\u00020\u00042\u0006\u0010%\u001a\u00020\u0004J\u0010\u0010)\u001a\u00020\b2\u0006\u0010\"\u001a\u00020#H\u0002J\u0010\u0010*\u001a\u00020\b2\u0006\u0010\"\u001a\u00020#H\u0002J\u0006\u0010+\u001a\u00020\bJ\u0016\u0010,\u001a\u00020\u00112\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010-\u001a\u00020\u0016J\u0006\u0010\u0007\u001a\u00020\bJ \u0010.\u001a\u00020\b2\u0006\u0010\"\u001a\u00020#2\u0006\u0010$\u001a\u00020\u00042\u0006\u0010%\u001a\u00020\u0004H\u0002J\u000e\u0010/\u001a\u00020\b2\u0006\u0010\"\u001a\u00020#J\u0018\u00100\u001a\u00020\b2\u0006\u00101\u001a\u00020#2\u0006\u00102\u001a\u00020\u0014H\u0002J\u0006\u00103\u001a\u00020\u0011J\b\u00104\u001a\u00020\u0011H\u0002J\u0014\u00105\u001a\u00020\u00112\f\u00106\u001a\b\u0012\u0004\u0012\u00020\u00110\u0010J\u0006\u00107\u001a\u00020\u0019R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000f\u001a\n\u0012\u0004\u0012\u00020\u0011\u0018\u00010\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00140\u0013X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0017\u001a\u0004\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00069"}, d2 = {"Lcom/touptek/measurerealize/utils/ThreeRectangleMeasureHelper;", "", "()V", "draggedPointIndex", "", "imageView", "Lcom/touptek/measurerealize/TpImageView;", "isDraggingPoint", "", "isInitialized", "lastTouchX", "", "lastTouchY", "longPressStartTime", "", "measurementUpdateCallback", "Lkotlin/Function0;", "", "measurements", "", "Lcom/touptek/measurerealize/utils/ThreeRectangleMeasurement;", "originalBitmap", "Landroid/graphics/Bitmap;", "selectedMeasurement", "addNewMeasurement", "", "cleanup", "clearSelection", "deleteSelectedMeasurement", "getAllMeasurements", "", "Lcom/touptek/measurerealize/utils/ThreeRectangleMeasurementData;", "getMeasurementCount", "handleTouchDown", "touchPoint", "Landroid/graphics/PointF;", "viewWidth", "viewHeight", "handleTouchEvent", "event", "Landroid/view/MotionEvent;", "handleTouchMove", "handleTouchUp", "hasSelectedMeasurement", "init", "bitmap", "isInImageContentArea", "isNearAnyMeasurement", "isPointInRotatedRectangle", "point", "measurement", "onScaleChanged", "resetInteractionState", "setMeasurementUpdateCallback", "callback", "startNewMeasurement", "Companion", "app_debug"})
public final class ThreeRectangleMeasureHelper {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.measurerealize.utils.ThreeRectangleMeasureHelper.Companion Companion = null;
    private static final java.lang.String TAG = "ThreeRectangleHelper";
    private static final float TOUCH_RADIUS = 80.0F;
    private static final long LONG_PRESS_DURATION = 800L;
    private static final float CLICK_TOLERANCE = 30.0F;
    private static final float DEFAULT_RECT_SIZE = 200.0F;
    private com.touptek.measurerealize.TpImageView imageView;
    private android.graphics.Bitmap originalBitmap;
    private boolean isInitialized = false;
    private final java.util.List<com.touptek.measurerealize.utils.ThreeRectangleMeasurement> measurements = null;
    private com.touptek.measurerealize.utils.ThreeRectangleMeasurement selectedMeasurement;
    private boolean isDraggingPoint = false;
    private int draggedPointIndex = -1;
    private long longPressStartTime = 0L;
    private float lastTouchX = 0.0F;
    private float lastTouchY = 0.0F;
    private kotlin.jvm.functions.Function0<kotlin.Unit> measurementUpdateCallback;
    
    public ThreeRectangleMeasureHelper() {
        super();
    }
    
    /**
     * 🚀 初始化助手
     */
    public final void init(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.TpImageView imageView, @org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
    }
    
    /**
     * 🔄 设置测量更新回调
     */
    public final void setMeasurementUpdateCallback(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    /**
     * 🎯 在屏幕中心创建默认三点矩形测量
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String startNewMeasurement() {
        return null;
    }
    
    /**
     * 🎯 添加新的三点矩形测量（在测量模式下）
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String addNewMeasurement() {
        return null;
    }
    
    /**
     * 🎯 处理触摸事件 - 支持三点控制逻辑
     */
    public final boolean handleTouchEvent(@org.jetbrains.annotations.NotNull
    android.view.MotionEvent event, int viewWidth, int viewHeight) {
        return false;
    }
    
    /**
     * 🎯 处理触摸按下事件
     */
    private final boolean handleTouchDown(android.graphics.PointF touchPoint, int viewWidth, int viewHeight) {
        return false;
    }
    
    /**
     * 🎯 处理触摸移动事件
     */
    private final boolean handleTouchMove(android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🎯 处理触摸抬起事件
     */
    private final boolean handleTouchUp(android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🛡️ 检测触摸点是否在图像内容区域（避免UI按钮区域）
     */
    private final boolean isInImageContentArea(android.graphics.PointF touchPoint, int viewWidth, int viewHeight) {
        return false;
    }
    
    /**
     * 🎯 检查点是否在旋转矩形内部
     */
    private final boolean isPointInRotatedRectangle(android.graphics.PointF point, com.touptek.measurerealize.utils.ThreeRectangleMeasurement measurement) {
        return false;
    }
    
    /**
     * 🔄 缩放变化时同步坐标
     */
    public final void onScaleChanged() {
    }
    
    /**
     * 📊 获取所有测量数据
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.touptek.measurerealize.utils.ThreeRectangleMeasurementData> getAllMeasurements() {
        return null;
    }
    
    /**
     * 🗑️ 删除选中的测量 - 智能删除逻辑
     */
    public final boolean deleteSelectedMeasurement() {
        return false;
    }
    
    /**
     * 🔄 重置交互状态
     */
    private final void resetInteractionState() {
    }
    
    /**
     * 🎯 检查是否正在拖拽点
     */
    public final boolean isDraggingPoint() {
        return false;
    }
    
    /**
     * 🔍 检查是否有选中的测量
     */
    public final boolean hasSelectedMeasurement() {
        return false;
    }
    
    /**
     * 📊 获取测量数量
     */
    public final int getMeasurementCount() {
        return 0;
    }
    
    /**
     * 🔍 检查是否靠近任何测量
     */
    public final boolean isNearAnyMeasurement(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🔄 清除所有选中状态
     */
    public final void clearSelection() {
    }
    
    /**
     * 🧹 清理资源
     */
    public final void cleanup() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/touptek/measurerealize/utils/ThreeRectangleMeasureHelper$Companion;", "", "()V", "CLICK_TOLERANCE", "", "DEFAULT_RECT_SIZE", "LONG_PRESS_DURATION", "", "TAG", "", "TOUCH_RADIUS", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}