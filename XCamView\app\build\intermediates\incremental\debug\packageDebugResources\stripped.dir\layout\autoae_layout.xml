<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/rounded_border"
    android:layout_margin="8dp">

    <CheckBox
        android:id="@+id/AECheckBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/colorISPText"
        android:text="@string/btn_auto_exposure"
        app:buttonTint="@color/colorISPBlue" />


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="307dp"
                android:layout_height="match_parent"
                android:text="@string/btn_exposure_compensation_colon"
                android:textColor="@color/colorISPText"
                android:textSize="18sp" />

            <TextView
                android:id="@+id/text_compensation_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="80"
                android:textAlignment="viewEnd"
                android:textColor="@color/colorISPText"
                android:textSize="18sp" />
        </LinearLayout>

        <LinearLayout

            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">
            <ImageButton
                android:id="@+id/btn_reduce_exposure_compensation"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/sub_n"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:layout_marginEnd="8dp"/>

            <SeekBar
                android:id="@+id/seekbar_compensation_tv"
                android:layout_width="255dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:max="25"
                android:progressBackgroundTint="@color/grey_background"
                android:progressTint="@color/colorISPBlue"
                android:thumbTint="@color/colorISPBlue" />

            <ImageButton
                android:id="@+id/btn_add_exposure_compensation"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/add_n"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:layout_marginEnd="8dp"/>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="307dp"
                android:layout_height="match_parent"
                android:text="@string/btn_exposure_time_colon"
                android:textColor="@color/colorISPText"
                android:textSize="18sp" />

            <TextView
                android:id="@+id/text_exposure_time_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="80"
                android:textAlignment="viewEnd"
                android:textColor="@color/colorISPText"
                android:textSize="18sp" />
        </LinearLayout>

        <LinearLayout

            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">


            <ImageButton
                android:id="@+id/btn_reduce_exposure_time"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/sub_n"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:layout_marginEnd="8dp"/>

            <SeekBar
                android:id="@+id/seekbar_exposure_time_tv"
                android:layout_width="255dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:max="1000"
                android:progressBackgroundTint="@color/grey_background"
                android:progressTint="@color/colorISPBlue"
                android:thumbTint="@color/colorISPBlue" />

            <ImageButton
                android:id="@+id/btn_add_exposure_time"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/add_n"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:layout_marginEnd="8dp"/>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="307dp"
                android:layout_height="match_parent"
                android:text="@string/btn_exposure_gain_colon"
                android:textColor="@color/colorISPText"
                android:textSize="18sp" />

            <TextView
                android:id="@+id/text_gain_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="80"
                android:textAlignment="viewEnd"
                android:textColor="@color/colorISPText"
                android:textSize="18sp" />
        </LinearLayout>

        <LinearLayout

            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">
            <ImageButton
                android:id="@+id/btn_reduce_gain"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/sub_n"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:layout_marginEnd="8dp"/>

            <SeekBar
                android:id="@+id/seekbar_gain_tv"
                android:layout_width="255dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:max="55"
                android:progressBackgroundTint="@color/grey_background"
                android:progressTint="@color/colorISPBlue"
                android:thumbTint="@color/colorISPBlue" />

            <ImageButton
                android:id="@+id/btn_add_gain"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/add_n"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:layout_marginEnd="8dp"/>
        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:orientation="vertical"
        android:gravity="center">

        <Button
            android:id="@+id/btn_Default_ae"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:text="Default" />
    </LinearLayout>



</LinearLayout>