<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res"><file name="red" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\color\red.xml" qualifiers="" type="color"/><file name="tab_text_color" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\color\tab_text_color.xml" qualifiers="" type="color"/><file name="about_d" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\about_d.png" qualifiers="" type="drawable"/><file name="about_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\about_n.png" qualifiers="" type="drawable"/><file name="add_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\add_n.png" qualifiers="" type="drawable"/><file name="bg_rounded_dialog_light" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\bg_rounded_dialog_light.xml" qualifiers="" type="drawable"/><file name="border_box" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\border_box.xml" qualifiers="" type="drawable"/><file name="bottom_panel_background" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\bottom_panel_background.xml" qualifiers="" type="drawable"/><file name="browser_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\browser_n.png" qualifiers="" type="drawable"/><file name="brow_d" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\brow_d.png" qualifiers="" type="drawable"/><file name="btn_about_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_about_n.png" qualifiers="" type="drawable"/><file name="btn_about_pressed" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_about_pressed.png" qualifiers="" type="drawable"/><file name="btn_color_adjustment_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_color_adjustment_n.png" qualifiers="" type="drawable"/><file name="btn_color_adjustment_pressed" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_color_adjustment_pressed.png" qualifiers="" type="drawable"/><file name="btn_confirm_bg" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_confirm_bg.xml" qualifiers="" type="drawable"/><file name="btn_draw_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_draw_n.png" qualifiers="" type="drawable"/><file name="btn_draw_pressed" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_draw_pressed.png" qualifiers="" type="drawable"/><file name="btn_exposure_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_exposure_n.png" qualifiers="" type="drawable"/><file name="btn_exposure_pressed" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_exposure_pressed.png" qualifiers="" type="drawable"/><file name="btn_flip_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_flip_n.png" qualifiers="" type="drawable"/><file name="btn_flip_pressed" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_flip_pressed.png" qualifiers="" type="drawable"/><file name="btn_folder_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_folder_n.png" qualifiers="" type="drawable"/><file name="btn_folder_pressed" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_folder_pressed.png" qualifiers="" type="drawable"/><file name="btn_image_processing_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_image_processing_n.png" qualifiers="" type="drawable"/><file name="btn_image_processing_pressed" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_image_processing_pressed.png" qualifiers="" type="drawable"/><file name="btn_menu_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_menu_n.png" qualifiers="" type="drawable"/><file name="btn_menu_pressed" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_menu_pressed.png" qualifiers="" type="drawable"/><file name="btn_pause_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_pause_n.png" qualifiers="" type="drawable"/><file name="btn_pause_pressed" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_pause_pressed.png" qualifiers="" type="drawable"/><file name="btn_power_frequency_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_power_frequency_n.png" qualifiers="" type="drawable"/><file name="btn_power_frequency_pressed" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_power_frequency_pressed.png" qualifiers="" type="drawable"/><file name="btn_record_video_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_record_video_n.png" qualifiers="" type="drawable"/><file name="btn_record_video_pressed" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_record_video_pressed.png" qualifiers="" type="drawable"/><file name="btn_settings_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_settings_n.png" qualifiers="" type="drawable"/><file name="btn_settings_pressed" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_settings_pressed.png" qualifiers="" type="drawable"/><file name="btn_take_photo_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_take_photo_n.png" qualifiers="" type="drawable"/><file name="btn_take_photo_pressed" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_take_photo_pressed.png" qualifiers="" type="drawable"/><file name="btn_white_balance_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_white_balance_n.png" qualifiers="" type="drawable"/><file name="btn_white_balance_pressed" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_white_balance_pressed.png" qualifiers="" type="drawable"/><file name="btn_zoom_in_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_zoom_in_n.png" qualifiers="" type="drawable"/><file name="btn_zoom_in_pressed" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_zoom_in_pressed.png" qualifiers="" type="drawable"/><file name="btn_zoom_out_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_zoom_out_n.png" qualifiers="" type="drawable"/><file name="btn_zoom_out_pressed" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\btn_zoom_out_pressed.png" qualifiers="" type="drawable"/><file name="button_border_selected" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\button_border_selected.xml" qualifiers="" type="drawable"/><file name="config_d" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\config_d.png" qualifiers="" type="drawable"/><file name="config_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\config_n.png" qualifiers="" type="drawable"/><file name="delete_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\delete_n.png" qualifiers="" type="drawable"/><file name="dialog_background" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\dialog_background.xml" qualifiers="" type="drawable"/><file name="dialog_border" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\dialog_border.xml" qualifiers="" type="drawable"/><file name="divider" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\divider.xml" qualifiers="" type="drawable"/><file name="divider_vertical_light" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\divider_vertical_light.xml" qualifiers="" type="drawable"/><file name="exposure_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\exposure_n.png" qualifiers="" type="drawable"/><file name="flip_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\flip_n.png" qualifiers="" type="drawable"/><file name="freeze_d" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\freeze_d.png" qualifiers="" type="drawable"/><file name="freeze_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\freeze_n.png" qualifiers="" type="drawable"/><file name="grey_background" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\grey_background.xml" qualifiers="" type="drawable"/><file name="groupbox_border" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\groupbox_border.xml" qualifiers="" type="drawable"/><file name="groupbox_title_background" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\groupbox_title_background.xml" qualifiers="" type="drawable"/><file name="home_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\home_n.png" qualifiers="" type="drawable"/><file name="hz_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\hz_n.png" qualifiers="" type="drawable"/><file name="ic_about" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_about.png" qualifiers="" type="drawable"/><file name="ic_action1" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_action1.png" qualifiers="" type="drawable"/><file name="ic_action2" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_action2.png" qualifiers="" type="drawable"/><file name="ic_action3" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_action3.png" qualifiers="" type="drawable"/><file name="ic_action4" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_action4.png" qualifiers="" type="drawable"/><file name="ic_action5" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_action5.png" qualifiers="" type="drawable"/><file name="ic_allselect" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_allselect.png" qualifiers="" type="drawable"/><file name="ic_angle_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_angle_n.png" qualifiers="" type="drawable"/><file name="ic_annotation_arrow_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_annotation_arrow_n.png" qualifiers="" type="drawable"/><file name="ic_annulus2_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_annulus2_n.png" qualifiers="" type="drawable"/><file name="ic_annulus_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_annulus_n.png" qualifiers="" type="drawable"/><file name="ic_arbline_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_arbline_n.png" qualifiers="" type="drawable"/><file name="ic_arc_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_arc_n.png" qualifiers="" type="drawable"/><file name="ic_arrowleft" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_arrowleft.png" qualifiers="" type="drawable"/><file name="ic_arrowright_d" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_arrowright_d.png" qualifiers="" type="drawable"/><file name="ic_calibration_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_calibration_n.png" qualifiers="" type="drawable"/><file name="ic_cancel" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_cancel.png" qualifiers="" type="drawable"/><file name="ic_cancel_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_cancel_n.png" qualifiers="" type="drawable"/><file name="ic_centerc_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_centerc_n.png" qualifiers="" type="drawable"/><file name="ic_checked" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_checked.xml" qualifiers="" type="drawable"/><file name="ic_close" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_close.png" qualifiers="" type="drawable"/><file name="ic_close_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_close_n.png" qualifiers="" type="drawable"/><file name="ic_color_adjust" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_color_adjust.png" qualifiers="" type="drawable"/><file name="ic_color_adjustment" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_color_adjustment.png" qualifiers="" type="drawable"/><file name="ic_config_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_config_n.png" qualifiers="" type="drawable"/><file name="ic_copy" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_copy.xml" qualifiers="" type="drawable"/><file name="ic_cut" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_cut.xml" qualifiers="" type="drawable"/><file name="ic_delete_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_delete_n.png" qualifiers="" type="drawable"/><file name="ic_details" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_details.xml" qualifiers="" type="drawable"/><file name="ic_draw" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_draw.png" qualifiers="" type="drawable"/><file name="ic_ellipse_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_ellipse_n.png" qualifiers="" type="drawable"/><file name="ic_export_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_export_n.png" qualifiers="" type="drawable"/><file name="ic_exposure" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_exposure.png" qualifiers="" type="drawable"/><file name="ic_fiveellipse_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_fiveellipse_n.png" qualifiers="" type="drawable"/><file name="ic_flip" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_flip.png" qualifiers="" type="drawable"/><file name="ic_fold" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_fold.png" qualifiers="" type="drawable"/><file name="ic_folder" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_folder.png" qualifiers="" type="drawable"/><file name="ic_fold_pressed" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_fold_pressed.png" qualifiers="" type="drawable"/><file name="ic_fourptangle_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_fourptangle_n.png" qualifiers="" type="drawable"/><file name="ic_hline_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_hline_n.png" qualifiers="" type="drawable"/><file name="ic_image_processing" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_image_processing.png" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_lock_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_lock_n.png" qualifiers="" type="drawable"/><file name="ic_menu" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_menu.png" qualifiers="" type="drawable"/><file name="ic_parallel_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_parallel_n.png" qualifiers="" type="drawable"/><file name="ic_paste" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_paste.xml" qualifiers="" type="drawable"/><file name="ic_pause" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_pause.png" qualifiers="" type="drawable"/><file name="ic_pic" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_pic.png" qualifiers="" type="drawable"/><file name="ic_picture" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_picture.png" qualifiers="" type="drawable"/><file name="ic_point_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_point_n.png" qualifiers="" type="drawable"/><file name="ic_polygon_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_polygon_n.png" qualifiers="" type="drawable"/><file name="ic_power_frequency" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_power_frequency.png" qualifiers="" type="drawable"/><file name="ic_preview" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_preview.png" qualifiers="" type="drawable"/><file name="ic_record_start" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_record_start.png" qualifiers="" type="drawable"/><file name="ic_record_video" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_record_video.png" qualifiers="" type="drawable"/><file name="ic_rectangle_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_rectangle_n.png" qualifiers="" type="drawable"/><file name="ic_return" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_return.png" qualifiers="" type="drawable"/><file name="ic_scale_bar_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_scale_bar_n.png" qualifiers="" type="drawable"/><file name="ic_select_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_select_n.png" qualifiers="" type="drawable"/><file name="ic_settings" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_settings.png" qualifiers="" type="drawable"/><file name="ic_storage" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_storage.png" qualifiers="" type="drawable"/><file name="ic_take_photo" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_take_photo.png" qualifiers="" type="drawable"/><file name="ic_text_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_text_n.png" qualifiers="" type="drawable"/><file name="ic_threecircle_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_threecircle_n.png" qualifiers="" type="drawable"/><file name="ic_threeline_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_threeline_n.png" qualifiers="" type="drawable"/><file name="ic_threepttwowircles_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_threepttwowircles_n.png" qualifiers="" type="drawable"/><file name="ic_threerectangle_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_threerectangle_n.png" qualifiers="" type="drawable"/><file name="ic_threevertical_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_threevertical_n.png" qualifiers="" type="drawable"/><file name="ic_unchecked" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_unchecked.xml" qualifiers="" type="drawable"/><file name="ic_video" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_video.png" qualifiers="" type="drawable"/><file name="ic_video_triangle" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_video_triangle.xml" qualifiers="" type="drawable"/><file name="ic_vline_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_vline_n.png" qualifiers="" type="drawable"/><file name="ic_wb_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_wb_n.png" qualifiers="" type="drawable"/><file name="ic_white" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_white.png" qualifiers="" type="drawable"/><file name="ic_white_balance" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_white_balance.png" qualifiers="" type="drawable"/><file name="ic_zoom_in" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_zoom_in.png" qualifiers="" type="drawable"/><file name="ic_zoom_out" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\ic_zoom_out.png" qualifiers="" type="drawable"/><file name="imageprocess_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\imageprocess_n.png" qualifiers="" type="drawable"/><file name="image_border_normal" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\image_border_normal.xml" qualifiers="" type="drawable"/><file name="isp_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\isp_n.png" qualifiers="" type="drawable"/><file name="measure_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\measure_n.png" qualifiers="" type="drawable"/><file name="nav_separator" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\nav_separator.xml" qualifiers="" type="drawable"/><file name="next_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\next_n.png" qualifiers="" type="drawable"/><file name="oval_button_background" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\oval_button_background.xml" qualifiers="" type="drawable"/><file name="popup_background" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\popup_background.xml" qualifiers="" type="drawable"/><file name="record_start_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\record_start_n.png" qualifiers="" type="drawable"/><file name="rounded_border" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\rounded_border.xml" qualifiers="" type="drawable"/><file name="rounded_button_background" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\rounded_button_background.xml" qualifiers="" type="drawable"/><file name="scenechange_d" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\scenechange_d.png" qualifiers="" type="drawable"/><file name="scenechange_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\scenechange_n.png" qualifiers="" type="drawable"/><file name="selector_settings_tab" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\selector_settings_tab.xml" qualifiers="" type="drawable"/><file name="selector_tab_background" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\selector_tab_background.xml" qualifiers="" type="drawable"/><file name="snap_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\snap_n.png" qualifiers="" type="drawable"/><file name="status_banner_bg" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\status_banner_bg.xml" qualifiers="" type="drawable"/><file name="stepframe_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\stepframe_n.png" qualifiers="" type="drawable"/><file name="sub_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\sub_n.png" qualifiers="" type="drawable"/><file name="tab_selected_bg" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\tab_selected_bg.xml" qualifiers="" type="drawable"/><file name="thumb_blue" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\thumb_blue.xml" qualifiers="" type="drawable"/><file name="thumb_blue_selector" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\thumb_blue_selector.xml" qualifiers="" type="drawable"/><file name="title_background" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\title_background.xml" qualifiers="" type="drawable"/><file name="tp_custom_enabled_selector" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\tp_custom_enabled_selector.xml" qualifiers="" type="drawable"/><file name="tp_custom_radionbutton" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\tp_custom_radionbutton.xml" qualifiers="" type="drawable"/><file name="tp_custom_seekbar" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\tp_custom_seekbar.xml" qualifiers="" type="drawable"/><file name="tp_custom_seekbar_thumb" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\tp_custom_seekbar_thumb.xml" qualifiers="" type="drawable"/><file name="tp_switch_thumb" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\tp_switch_thumb.xml" qualifiers="" type="drawable"/><file name="tp_switch_track" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\tp_switch_track.xml" qualifiers="" type="drawable"/><file name="track_blue_selector" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\track_blue_selector.xml" qualifiers="" type="drawable"/><file name="zoomin_d" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\zoomin_d.png" qualifiers="" type="drawable"/><file name="zoomin_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\zoomin_n.png" qualifiers="" type="drawable"/><file name="zoomout_d" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\zoomout_d.png" qualifiers="" type="drawable"/><file name="zoomout_n" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable\zoomout_n.png" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\drawable-v24\ic_launcher_foreground.xml" qualifiers="v24" type="drawable"/><file name="kai" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\font\kai.ttf" qualifiers="" type="font"/><file name="song" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\font\song.ttf" qualifiers="" type="font"/><file name="activity_main" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_touptek" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\activity_touptek.xml" qualifiers="" type="layout"/><file name="activity_touptek_btn" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\activity_touptek_btn.xml" qualifiers="" type="layout"/><file name="activity_welcome" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\activity_welcome.xml" qualifiers="" type="layout"/><file name="autoae_layout" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\autoae_layout.xml" qualifiers="" type="layout"/><file name="browse_grid_layout" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\browse_grid_layout.xml" qualifiers="" type="layout"/><file name="browse_layout" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\browse_layout.xml" qualifiers="" type="layout"/><file name="copydialog_settings" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\copydialog_settings.xml" qualifiers="" type="layout"/><file name="dialog_file_details" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\dialog_file_details.xml" qualifiers="" type="layout"/><file name="dialog_modern_settings" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\dialog_modern_settings.xml" qualifiers="" type="layout"/><file name="dialog_settings" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\dialog_settings.xml" qualifiers="" type="layout"/><file name="flip_layout" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\flip_layout.xml" qualifiers="" type="layout"/><file name="folder_item" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\folder_item.xml" qualifiers="" type="layout"/><file name="fragment_format_settings" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\fragment_format_settings.xml" qualifiers="" type="layout"/><file name="fragment_measurement_settings" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\fragment_measurement_settings.xml" qualifiers="" type="layout"/><file name="fragment_network_settings" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\fragment_network_settings.xml" qualifiers="" type="layout"/><file name="fragment_storage_settings" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\fragment_storage_settings.xml" qualifiers="" type="layout"/><file name="hz_layout" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\hz_layout.xml" qualifiers="" type="layout"/><file name="image_parameter_2_layout" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\image_parameter_2_layout.xml" qualifiers="" type="layout"/><file name="image_parameter_layout" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\image_parameter_layout.xml" qualifiers="" type="layout"/><file name="image_viewer" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\image_viewer.xml" qualifiers="" type="layout"/><file name="layout_input_info_item" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\layout_input_info_item.xml" qualifiers="" type="layout"/><file name="measurement_layout" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\measurement_layout.xml" qualifiers="" type="layout"/><file name="operation_grid_layout" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\operation_grid_layout.xml" qualifiers="" type="layout"/><file name="popup_config_menu" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\popup_config_menu.xml" qualifiers="" type="layout"/><file name="popup_menu_layout" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\popup_menu_layout.xml" qualifiers="" type="layout"/><file name="right_panel_layout" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\right_panel_layout.xml" qualifiers="" type="layout"/><file name="settings_misc" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\settings_misc.xml" qualifiers="" type="layout"/><file name="settings_record" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\settings_record.xml" qualifiers="" type="layout"/><file name="testdialog_settings" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\testdialog_settings.xml" qualifiers="" type="layout"/><file name="videodecode_layout" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\videodecode_layout.xml" qualifiers="" type="layout"/><file name="video_layout" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\video_layout.xml" qualifiers="" type="layout"/><file name="whitebalance_layout" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\layout\whitebalance_layout.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\mipmap-hdpi\ic_launcher_round.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\mipmap-mdpi\ic_launcher_round.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\mipmap-xhdpi\ic_launcher_round.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\values\arrays.xml" qualifiers=""><string-array name="scene_options">
        <item>生物</item>
        <item>体视</item>
    </string-array><string-array name="label_styles">
        <item>标准标注</item>
        <item>带边框标注</item>
        <item>透明背景标注</item>
    </string-array><string-array name="font_sizes">
        <item>小号字体</item>
        <item>中号字体</item>
        <item>大号字体</item>
    </string-array><string-array name="compression_levels">
        <item>无损</item>
        <item>高</item>
        <item>标准</item>
        <item>快速</item>
    </string-array></file><file path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\values\attrs.xml" qualifiers=""><declare-styleable name="RoundelMenu">
        <attr format="color|reference" name="round_menu_roundColor"/>
        <attr format="color|reference" name="round_menu_centerColor"/>
        <attr format="dimension" name="round_menu_expandedRadius"/>
        <attr format="dimension" name="round_menu_collapsedRadius"/>
        <attr format="integer" name="round_menu_duration"/>
        <attr format="integer" name="round_menu_item_anim_delay"/>
        <attr format="dimension" name="round_menu_item_width"/>
    </declare-styleable></file><file path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white_background">#FFFFFF</color><color name="grey_background">#AAAAAA</color><color name="border_color">#999999</color><color name="solid_blue">#0000FF</color><color name="Light_black">#FF2E2E2E</color><color name="popup_text_color">#FFFFFF</color><color name="popup_background_color">#555555</color><color name="gray_text_disabled">#A0A0A0</color><color name="gray_icon_disabled">#B0B0B0</color><color name="colorPrimary">#FFA0A0A0</color><color name="colorISPText">#333333</color><color name="colorISPBlue">#FF2196F3</color><color name="blue_500">#FF2196F3</color><color name="gray_300">#FFE0E0E0</color><color name="white">#FFFFFFFF</color><color name="dialog_border">#888888</color><color name="primary_color">#2196F3</color><color name="primary_dark_color">#1976D2</color><color name="gray_text">#666666</color></file><file path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="folder_item_width">300dp</dimen></file><file path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">XCamView</string><string name="btn_record_start">start record</string><string name="btn_pq_start">start pq</string><string name="btn_pq_stop">end pq</string><string name="btn_calc_luma_start">start luma</string><string name="btn_calc_luma_stop">end luma</string><string name="btn_lf_range_start">start assign range</string><string name="btn_lf_range_stop">end assign range</string><string name="screenshot_failed">screenshot failed</string><string name="menu"/><string name="draw"/><string name="about"/><string name="settings"/><string name="zoom_out"/><string name="zoom_in"/><string name="folder"/><string name="pause"/><string name="record_video"/><string name="take_photo"/><string name="btn_add">+</string><string name="btn_reduce">-</string><string name="btn_auto_exposure">Auto Exposure</string><string name="btn_exposure_compensation_colon">Exposure Compensation：</string><string name="btn_exposure_time_colon">Exposure Time：</string><string name="btn_exposure_gain_colon">Gain：</string><string name="title_wb_red_text">Red：</string><string name="title_wb_green_text">Green：</string><string name="title_wb_blue_text">Blue：</string><string name="title_saturation_text">Saturation：</string><string name="title_gamma_text">Gamma：</string><string name="title_contrast_text">Contrast：</string><string name="title_brightness_text">Brightness：</string><string name="title_sharpness_text">Sharpness：</string><string name="title_denoise_text">Denoise：</string><string name="button_desc_calibration">校准</string><string name="button_desc_export">导出</string><string name="button_desc_delete_measurement">删除测量</string><string name="button_desc_angle">角度测量</string><string name="button_desc_angle_three">三点法画角度</string><string name="button_desc_point">点测量</string><string name="button_desc_arb_line">任意线</string><string name="button_desc_three_line">三点画线测量</string><string name="button_desc_horizon_line">水平线</string><string name="button_desc_vertical_line">垂直线</string><string name="button_desc_parallel_line">平行线</string><string name="button_desc_three_vertical">三点画垂线</string><string name="button_desc_rectangle">矩形</string><string name="button_desc_three_rectangle">三点画矩形</string><string name="button_desc_ellipse">椭圆</string><string name="button_desc_five_ellipse">五点画椭圆</string><string name="button_desc_center_circle">中心圆</string><string name="button_desc_three_circle">三点画圆环</string><string name="button_desc_annulus">环形</string><string name="button_desc_annulus2">双环</string><string name="button_desc_twocircles">双圆</string><string name="button_desc_three_twocircles">三点画双圆</string><string name="button_desc_arc">圆弧</string><string name="menu_copy">复制到</string><string name="menu_cut">移动到</string><string name="menu_paste">粘贴</string><string name="menu_details">详细信息</string><string-array name="precision_options">
        <item>0位小数</item>
        <item>1位小数</item>
        <item>2位小数</item>
        <item>3位小数</item>
        <item>4位小数</item>
        <item>5位小数</item>
        <item>6位小数</item>
    </string-array><string-array name="unit_options">
        <item>um (微米)</item>
        <item>mm (毫米)</item>
        <item>cm (厘米)</item>
        <item>m (米)</item>
        <item>inch (英寸)</item>
        <item>mil (密耳)</item>
    </string-array><string name="select_precision">选择测量精度</string><string name="select_default_unit">选择默认单位</string></file><file path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\values\styles.xml" qualifiers=""><style name="FullScreenDialog" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style><style name="ModernSettingTab">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:padding">16dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:background">@drawable/grey_background</item>
    </style><style name="ModernActionButton">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_margin">16dp</item>
        <item name="android:textColor">@color/white_background</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="cornerRadius">8dp</item>
        <item name="backgroundTint">@color/gray_text_disabled</item>
    </style></file><file path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"/><file name="backup_rules" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="E:\work\Andriod\XcamView_all\XCamView\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\work\Andriod\XcamView_all\XCamView\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\work\Andriod\XcamView_all\XCamView\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\work\Andriod\XcamView_all\XCamView\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\work\Andriod\XcamView_all\XCamView\app\build\generated\res\resValues\debug"/></dataSet><mergedItems><configuration qualifiers=""><declare-styleable name="RoundelMenu">
        <attr format="color|reference" name="round_menu_roundColor"/>
        <attr format="color|reference" name="round_menu_centerColor"/>
        <attr format="dimension" name="round_menu_expandedRadius"/>
        <attr format="dimension" name="round_menu_collapsedRadius"/>
        <attr format="integer" name="round_menu_duration"/>
        <attr format="integer" name="round_menu_item_anim_delay"/>
        <attr format="dimension" name="round_menu_item_width"/>
    </declare-styleable></configuration></mergedItems></merger>