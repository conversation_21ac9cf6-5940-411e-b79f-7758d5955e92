package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 🎯 三点圆测量实例 - 专业级数据结构
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b\"\n\u0002\u0018\u0002\n\u0002\b\u0015\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\b\b\u0086\b\u0018\u0000 `2\u00020\u0001:\u0001`B\u009b\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\t\u0012\b\b\u0002\u0010\u000b\u001a\u00020\t\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0006\u0012\b\b\u0002\u0010\r\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u000e\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0011\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0011\u0012\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0011\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u000e\u00a2\u0006\u0002\u0010\u0015J\u0006\u0010.\u001a\u00020\u0011J \u0010/\u001a\u00020\u00112\u0006\u00100\u001a\u00020\u00062\u0006\u00101\u001a\u00020\u00062\u0006\u00102\u001a\u00020\u0006H\u0002J\u0018\u00103\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\t04J\u0006\u00105\u001a\u00020\u0011J\u0006\u00106\u001a\u00020\u0011J\u0006\u00107\u001a\u00020\u0011J\u0006\u00108\u001a\u00020\u0006J\t\u00109\u001a\u00020\u0003H\u00c6\u0003J\u0010\u0010:\u001a\u0004\u0018\u00010\u0011H\u00c2\u0003\u00a2\u0006\u0002\u0010;J\u0010\u0010<\u001a\u0004\u0018\u00010\u0011H\u00c2\u0003\u00a2\u0006\u0002\u0010;J\u0010\u0010=\u001a\u0004\u0018\u00010\u0011H\u00c2\u0003\u00a2\u0006\u0002\u0010;J\t\u0010>\u001a\u00020\u000eH\u00c2\u0003J\u000f\u0010?\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\u000f\u0010@\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\t\u0010A\u001a\u00020\tH\u00c6\u0003J\t\u0010B\u001a\u00020\tH\u00c6\u0003J\t\u0010C\u001a\u00020\tH\u00c6\u0003J\u000b\u0010D\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\t\u0010E\u001a\u00020\u000eH\u00c6\u0003J\t\u0010F\u001a\u00020\u000eH\u00c6\u0003J\u0018\u0010G\u001a\u00020\u00062\u0006\u0010H\u001a\u00020\u00062\u0006\u0010I\u001a\u00020JH\u0002J\u0018\u0010K\u001a\u00020\u00062\u0006\u0010L\u001a\u00020\u00062\u0006\u0010I\u001a\u00020JH\u0002J\u00a4\u0001\u0010M\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\t2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u00062\b\b\u0002\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u000e2\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u00112\b\b\u0002\u0010\u0014\u001a\u00020\u000eH\u00c6\u0001\u00a2\u0006\u0002\u0010NJ\u0013\u0010O\u001a\u00020\t2\b\u0010P\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u000e\u0010Q\u001a\u00020R2\u0006\u0010S\u001a\u00020\u0006J\t\u0010T\u001a\u00020RH\u00d6\u0001J\b\u0010U\u001a\u00020VH\u0002J\u0016\u0010W\u001a\u00020\t2\u0006\u0010S\u001a\u00020\u00062\u0006\u0010X\u001a\u00020YJ\u000e\u0010Z\u001a\u00020V2\u0006\u0010I\u001a\u00020JJ\u000e\u0010[\u001a\u00020V2\u0006\u0010I\u001a\u00020JJ\t\u0010\\\u001a\u00020\u0003H\u00d6\u0001J\u0016\u0010]\u001a\u00020V2\u0006\u0010^\u001a\u00020R2\u0006\u0010_\u001a\u00020\u0006R \u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0016\u0010\u0017\"\u0004\b\u0018\u0010\u0019R\u000e\u0010\u0014\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0012\u0010\u0012\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010\u001aR\u0012\u0010\u0013\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010\u001aR\u0012\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010\u001aR\u001a\u0010\r\u001a\u00020\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001b\u0010\u001c\"\u0004\b\u001d\u0010\u001eR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010 R\u001a\u0010\u000b\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000b\u0010!\"\u0004\b\"\u0010#R\u001a\u0010\n\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\n\u0010!\"\u0004\b$\u0010#R\u001a\u0010\b\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\b\u0010!\"\u0004\b%\u0010#R\u001a\u0010\u000f\u001a\u00020\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b&\u0010\u001c\"\u0004\b\'\u0010\u001eR\u001c\u0010\f\u001a\u0004\u0018\u00010\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b(\u0010)\"\u0004\b*\u0010+R \u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b,\u0010\u0017\"\u0004\b-\u0010\u0019\u00a8\u0006a"}, d2 = {"Lcom/touptek/measurerealize/utils/ThreeCircleMeasurement;", "", "id", "", "viewPoints", "", "Landroid/graphics/PointF;", "bitmapPoints", "isSelected", "", "isEditing", "isCompleted", "textPosition", "creationTime", "", "lastModified", "cachedRadius", "", "cachedArea", "cachedPerimeter", "cacheValidTime", "(Ljava/lang/String;Ljava/util/List;Ljava/util/List;ZZZLandroid/graphics/PointF;JJLjava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;J)V", "getBitmapPoints", "()Ljava/util/List;", "setBitmapPoints", "(Ljava/util/List;)V", "Ljava/lang/Double;", "getCreationTime", "()J", "setCreationTime", "(J)V", "getId", "()Ljava/lang/String;", "()Z", "setCompleted", "(Z)V", "setEditing", "setSelected", "getLastModified", "setLastModified", "getTextPosition", "()Landroid/graphics/PointF;", "setTextPosition", "(Landroid/graphics/PointF;)V", "getViewPoints", "setViewPoints", "calculateArea", "calculateCircleFromBitmapPoints", "p1", "p2", "p3", "calculateCircleFromThreePoints", "Lkotlin/Triple;", "calculateCircumference", "calculatePerimeter", "calculateRadius", "calculateTextPosition", "component1", "component10", "()Ljava/lang/Double;", "component11", "component12", "component13", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "convertBitmapToViewCoords", "bitmapPoint", "imageView", "Lcom/touptek/measurerealize/TpImageView;", "convertViewToBitmapCoords", "viewPoint", "copy", "(Ljava/lang/String;Ljava/util/List;Ljava/util/List;ZZZLandroid/graphics/PointF;JJLjava/lang/Double;Ljava/lang/Double;Ljava/lang/Double;J)Lcom/touptek/measurerealize/utils/ThreeCircleMeasurement;", "equals", "other", "getNearestPointIndex", "", "touchPoint", "hashCode", "invalidateCache", "", "isPointInTouchRange", "touchRadius", "", "syncBitmapCoords", "syncViewCoords", "toString", "updateWithThreePointConstraint", "pointIndex", "newPoint", "Companion", "app_debug"})
public final class ThreeCircleMeasurement {
    @org.jetbrains.annotations.NotNull
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull
    private java.util.List<android.graphics.PointF> viewPoints;
    @org.jetbrains.annotations.NotNull
    private java.util.List<android.graphics.PointF> bitmapPoints;
    private boolean isSelected;
    private boolean isEditing;
    private boolean isCompleted;
    @org.jetbrains.annotations.Nullable
    private android.graphics.PointF textPosition;
    private long creationTime;
    private long lastModified;
    private java.lang.Double cachedRadius;
    private java.lang.Double cachedArea;
    private java.lang.Double cachedPerimeter;
    private long cacheValidTime;
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.measurerealize.utils.ThreeCircleMeasurement.Companion Companion = null;
    private static final long CACHE_VALIDITY_MS = 100L;
    
    /**
     * 🎯 三点圆测量实例 - 专业级数据结构
     */
    @org.jetbrains.annotations.NotNull
    public final com.touptek.measurerealize.utils.ThreeCircleMeasurement copy(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> viewPoints, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> bitmapPoints, boolean isSelected, boolean isEditing, boolean isCompleted, @org.jetbrains.annotations.Nullable
    android.graphics.PointF textPosition, long creationTime, long lastModified, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedRadius, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedArea, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedPerimeter, long cacheValidTime) {
        return null;
    }
    
    /**
     * 🎯 三点圆测量实例 - 专业级数据结构
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 🎯 三点圆测量实例 - 专业级数据结构
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 🎯 三点圆测量实例 - 专业级数据结构
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public ThreeCircleMeasurement() {
        super();
    }
    
    public ThreeCircleMeasurement(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> viewPoints, @org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> bitmapPoints, boolean isSelected, boolean isEditing, boolean isCompleted, @org.jetbrains.annotations.Nullable
    android.graphics.PointF textPosition, long creationTime, long lastModified, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedRadius, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedArea, @org.jetbrains.annotations.Nullable
    java.lang.Double cachedPerimeter, long cacheValidTime) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> getViewPoints() {
        return null;
    }
    
    public final void setViewPoints(@org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> p0) {
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> getBitmapPoints() {
        return null;
    }
    
    public final void setBitmapPoints(@org.jetbrains.annotations.NotNull
    java.util.List<android.graphics.PointF> p0) {
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean isSelected() {
        return false;
    }
    
    public final void setSelected(boolean p0) {
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final boolean isEditing() {
        return false;
    }
    
    public final void setEditing(boolean p0) {
    }
    
    public final boolean component6() {
        return false;
    }
    
    public final boolean isCompleted() {
        return false;
    }
    
    public final void setCompleted(boolean p0) {
    }
    
    @org.jetbrains.annotations.Nullable
    public final android.graphics.PointF component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final android.graphics.PointF getTextPosition() {
        return null;
    }
    
    public final void setTextPosition(@org.jetbrains.annotations.Nullable
    android.graphics.PointF p0) {
    }
    
    public final long component8() {
        return 0L;
    }
    
    public final long getCreationTime() {
        return 0L;
    }
    
    public final void setCreationTime(long p0) {
    }
    
    public final long component9() {
        return 0L;
    }
    
    public final long getLastModified() {
        return 0L;
    }
    
    public final void setLastModified(long p0) {
    }
    
    private final java.lang.Double component10() {
        return null;
    }
    
    private final java.lang.Double component11() {
        return null;
    }
    
    private final java.lang.Double component12() {
        return null;
    }
    
    private final long component13() {
        return 0L;
    }
    
    /**
     * 🎯 根据三个点计算圆心和半径 - 使用视图坐标进行显示计算
     */
    @org.jetbrains.annotations.NotNull
    public final kotlin.Triple<android.graphics.PointF, java.lang.Double, java.lang.Boolean> calculateCircleFromThreePoints() {
        return null;
    }
    
    /**
     * 🎯 计算半径（使用位图坐标 - 真实半径，不受缩放影响）
     */
    public final double calculateRadius() {
        return 0.0;
    }
    
    /**
     * 🎯 基于位图坐标计算圆的半径
     */
    private final double calculateCircleFromBitmapPoints(android.graphics.PointF p1, android.graphics.PointF p2, android.graphics.PointF p3) {
        return 0.0;
    }
    
    /**
     * 🎯 计算面积（使用缓存）
     */
    public final double calculateArea() {
        return 0.0;
    }
    
    /**
     * 🎯 计算周长（使用缓存）
     */
    public final double calculatePerimeter() {
        return 0.0;
    }
    
    /**
     * ⭕ 计算圆周长
     */
    public final double calculateCircumference() {
        return 0.0;
    }
    
    /**
     * 🎯 检查点是否在触摸范围内
     */
    public final boolean isPointInTouchRange(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint, float touchRadius) {
        return false;
    }
    
    /**
     * 🎯 获取最近的点索引
     */
    public final int getNearestPointIndex(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return 0;
    }
    
    /**
     * 🎯 更新三点圆约束
     */
    public final void updateWithThreePointConstraint(int pointIndex, @org.jetbrains.annotations.NotNull
    android.graphics.PointF newPoint) {
    }
    
    /**
     * 🎯 同步位图坐标
     */
    public final void syncBitmapCoords(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.TpImageView imageView) {
    }
    
    /**
     * 🎯 同步视图坐标
     */
    public final void syncViewCoords(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.TpImageView imageView) {
    }
    
    /**
     * 🎯 坐标转换：视图坐标 -> 位图坐标
     */
    private final android.graphics.PointF convertViewToBitmapCoords(android.graphics.PointF viewPoint, com.touptek.measurerealize.TpImageView imageView) {
        return null;
    }
    
    /**
     * 🎯 坐标转换：位图坐标 -> 视图坐标
     */
    private final android.graphics.PointF convertBitmapToViewCoords(android.graphics.PointF bitmapPoint, com.touptek.measurerealize.TpImageView imageView) {
        return null;
    }
    
    /**
     * 🎯 计算文本显示位置 - 避免遮挡圆心标记
     */
    @org.jetbrains.annotations.NotNull
    public final android.graphics.PointF calculateTextPosition() {
        return null;
    }
    
    /**
     * 清除缓存
     */
    private final void invalidateCache() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/touptek/measurerealize/utils/ThreeCircleMeasurement$Companion;", "", "()V", "CACHE_VALIDITY_MS", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}