// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.measurerealize.TpImageView;
import com.touptek.measurerealize.utils.MeasurementOverlayView;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ImageViewerBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final ImageButton btnAngle;

  @NonNull
  public final ImageButton btnAngleFour;

  @NonNull
  public final ImageButton btnAnnulus;

  @NonNull
  public final ImageButton btnAnnulus2;

  @NonNull
  public final ImageButton btnArbLine;

  @NonNull
  public final ImageButton btnArc;

  @NonNull
  public final Button btnBack;

  @NonNull
  public final ImageButton btnCalibration;

  @NonNull
  public final ImageButton btnCenterCircle;

  @NonNull
  public final ImageButton btnDeleteMeasurement;

  @NonNull
  public final ImageButton btnEllipse;

  @NonNull
  public final ImageButton btnFiveEllipse;

  @NonNull
  public final ImageButton btnHorizonLine;

  @NonNull
  public final Button btnNext;

  @NonNull
  public final ImageButton btnParallelLine;

  @NonNull
  public final ImageButton btnPoint;

  @NonNull
  public final Button btnPrevious;

  @NonNull
  public final ImageButton btnRectangle;

  @NonNull
  public final ImageButton btnThreeCircle;

  @NonNull
  public final ImageButton btnThreeLine;

  @NonNull
  public final ImageButton btnThreeRectangle;

  @NonNull
  public final ImageButton btnThreeTwocircles;

  @NonNull
  public final ImageButton btnThreeVertical;

  @NonNull
  public final ImageButton btnTwocircles;

  @NonNull
  public final ImageButton btnVerticalLine;

  @NonNull
  public final LinearLayout buttonPanel;

  @NonNull
  public final TpImageView imageView;

  @NonNull
  public final MeasurementOverlayView measurementOverlay;

  @NonNull
  public final LinearLayout measurementToolbar;

  private ImageViewerBinding(@NonNull FrameLayout rootView, @NonNull ImageButton btnAngle,
      @NonNull ImageButton btnAngleFour, @NonNull ImageButton btnAnnulus,
      @NonNull ImageButton btnAnnulus2, @NonNull ImageButton btnArbLine,
      @NonNull ImageButton btnArc, @NonNull Button btnBack, @NonNull ImageButton btnCalibration,
      @NonNull ImageButton btnCenterCircle, @NonNull ImageButton btnDeleteMeasurement,
      @NonNull ImageButton btnEllipse, @NonNull ImageButton btnFiveEllipse,
      @NonNull ImageButton btnHorizonLine, @NonNull Button btnNext,
      @NonNull ImageButton btnParallelLine, @NonNull ImageButton btnPoint,
      @NonNull Button btnPrevious, @NonNull ImageButton btnRectangle,
      @NonNull ImageButton btnThreeCircle, @NonNull ImageButton btnThreeLine,
      @NonNull ImageButton btnThreeRectangle, @NonNull ImageButton btnThreeTwocircles,
      @NonNull ImageButton btnThreeVertical, @NonNull ImageButton btnTwocircles,
      @NonNull ImageButton btnVerticalLine, @NonNull LinearLayout buttonPanel,
      @NonNull TpImageView imageView, @NonNull MeasurementOverlayView measurementOverlay,
      @NonNull LinearLayout measurementToolbar) {
    this.rootView = rootView;
    this.btnAngle = btnAngle;
    this.btnAngleFour = btnAngleFour;
    this.btnAnnulus = btnAnnulus;
    this.btnAnnulus2 = btnAnnulus2;
    this.btnArbLine = btnArbLine;
    this.btnArc = btnArc;
    this.btnBack = btnBack;
    this.btnCalibration = btnCalibration;
    this.btnCenterCircle = btnCenterCircle;
    this.btnDeleteMeasurement = btnDeleteMeasurement;
    this.btnEllipse = btnEllipse;
    this.btnFiveEllipse = btnFiveEllipse;
    this.btnHorizonLine = btnHorizonLine;
    this.btnNext = btnNext;
    this.btnParallelLine = btnParallelLine;
    this.btnPoint = btnPoint;
    this.btnPrevious = btnPrevious;
    this.btnRectangle = btnRectangle;
    this.btnThreeCircle = btnThreeCircle;
    this.btnThreeLine = btnThreeLine;
    this.btnThreeRectangle = btnThreeRectangle;
    this.btnThreeTwocircles = btnThreeTwocircles;
    this.btnThreeVertical = btnThreeVertical;
    this.btnTwocircles = btnTwocircles;
    this.btnVerticalLine = btnVerticalLine;
    this.buttonPanel = buttonPanel;
    this.imageView = imageView;
    this.measurementOverlay = measurementOverlay;
    this.measurementToolbar = measurementToolbar;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ImageViewerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ImageViewerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.image_viewer, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ImageViewerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_angle;
      ImageButton btnAngle = ViewBindings.findChildViewById(rootView, id);
      if (btnAngle == null) {
        break missingId;
      }

      id = R.id.btn_angle_four;
      ImageButton btnAngleFour = ViewBindings.findChildViewById(rootView, id);
      if (btnAngleFour == null) {
        break missingId;
      }

      id = R.id.btn_annulus;
      ImageButton btnAnnulus = ViewBindings.findChildViewById(rootView, id);
      if (btnAnnulus == null) {
        break missingId;
      }

      id = R.id.btn_annulus2;
      ImageButton btnAnnulus2 = ViewBindings.findChildViewById(rootView, id);
      if (btnAnnulus2 == null) {
        break missingId;
      }

      id = R.id.btn_arb_line;
      ImageButton btnArbLine = ViewBindings.findChildViewById(rootView, id);
      if (btnArbLine == null) {
        break missingId;
      }

      id = R.id.btn_arc;
      ImageButton btnArc = ViewBindings.findChildViewById(rootView, id);
      if (btnArc == null) {
        break missingId;
      }

      id = R.id.btn_back;
      Button btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btn_calibration;
      ImageButton btnCalibration = ViewBindings.findChildViewById(rootView, id);
      if (btnCalibration == null) {
        break missingId;
      }

      id = R.id.btn_center_circle;
      ImageButton btnCenterCircle = ViewBindings.findChildViewById(rootView, id);
      if (btnCenterCircle == null) {
        break missingId;
      }

      id = R.id.btn_delete_measurement;
      ImageButton btnDeleteMeasurement = ViewBindings.findChildViewById(rootView, id);
      if (btnDeleteMeasurement == null) {
        break missingId;
      }

      id = R.id.btn_ellipse;
      ImageButton btnEllipse = ViewBindings.findChildViewById(rootView, id);
      if (btnEllipse == null) {
        break missingId;
      }

      id = R.id.btn_five_ellipse;
      ImageButton btnFiveEllipse = ViewBindings.findChildViewById(rootView, id);
      if (btnFiveEllipse == null) {
        break missingId;
      }

      id = R.id.btn_horizon_line;
      ImageButton btnHorizonLine = ViewBindings.findChildViewById(rootView, id);
      if (btnHorizonLine == null) {
        break missingId;
      }

      id = R.id.btn_next;
      Button btnNext = ViewBindings.findChildViewById(rootView, id);
      if (btnNext == null) {
        break missingId;
      }

      id = R.id.btn_parallel_line;
      ImageButton btnParallelLine = ViewBindings.findChildViewById(rootView, id);
      if (btnParallelLine == null) {
        break missingId;
      }

      id = R.id.btn_point;
      ImageButton btnPoint = ViewBindings.findChildViewById(rootView, id);
      if (btnPoint == null) {
        break missingId;
      }

      id = R.id.btn_previous;
      Button btnPrevious = ViewBindings.findChildViewById(rootView, id);
      if (btnPrevious == null) {
        break missingId;
      }

      id = R.id.btn_rectangle;
      ImageButton btnRectangle = ViewBindings.findChildViewById(rootView, id);
      if (btnRectangle == null) {
        break missingId;
      }

      id = R.id.btn_three_circle;
      ImageButton btnThreeCircle = ViewBindings.findChildViewById(rootView, id);
      if (btnThreeCircle == null) {
        break missingId;
      }

      id = R.id.btn_three_line;
      ImageButton btnThreeLine = ViewBindings.findChildViewById(rootView, id);
      if (btnThreeLine == null) {
        break missingId;
      }

      id = R.id.btn_three_rectangle;
      ImageButton btnThreeRectangle = ViewBindings.findChildViewById(rootView, id);
      if (btnThreeRectangle == null) {
        break missingId;
      }

      id = R.id.btn_three_twocircles;
      ImageButton btnThreeTwocircles = ViewBindings.findChildViewById(rootView, id);
      if (btnThreeTwocircles == null) {
        break missingId;
      }

      id = R.id.btn_three_vertical;
      ImageButton btnThreeVertical = ViewBindings.findChildViewById(rootView, id);
      if (btnThreeVertical == null) {
        break missingId;
      }

      id = R.id.btn_twocircles;
      ImageButton btnTwocircles = ViewBindings.findChildViewById(rootView, id);
      if (btnTwocircles == null) {
        break missingId;
      }

      id = R.id.btn_vertical_line;
      ImageButton btnVerticalLine = ViewBindings.findChildViewById(rootView, id);
      if (btnVerticalLine == null) {
        break missingId;
      }

      id = R.id.button_panel;
      LinearLayout buttonPanel = ViewBindings.findChildViewById(rootView, id);
      if (buttonPanel == null) {
        break missingId;
      }

      id = R.id.image_view;
      TpImageView imageView = ViewBindings.findChildViewById(rootView, id);
      if (imageView == null) {
        break missingId;
      }

      id = R.id.measurement_overlay;
      MeasurementOverlayView measurementOverlay = ViewBindings.findChildViewById(rootView, id);
      if (measurementOverlay == null) {
        break missingId;
      }

      id = R.id.measurement_toolbar;
      LinearLayout measurementToolbar = ViewBindings.findChildViewById(rootView, id);
      if (measurementToolbar == null) {
        break missingId;
      }

      return new ImageViewerBinding((FrameLayout) rootView, btnAngle, btnAngleFour, btnAnnulus,
          btnAnnulus2, btnArbLine, btnArc, btnBack, btnCalibration, btnCenterCircle,
          btnDeleteMeasurement, btnEllipse, btnFiveEllipse, btnHorizonLine, btnNext,
          btnParallelLine, btnPoint, btnPrevious, btnRectangle, btnThreeCircle, btnThreeLine,
          btnThreeRectangle, btnThreeTwocircles, btnThreeVertical, btnTwocircles, btnVerticalLine,
          buttonPanel, imageView, measurementOverlay, measurementToolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
