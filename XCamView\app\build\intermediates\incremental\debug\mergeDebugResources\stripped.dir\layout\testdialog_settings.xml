<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/dialog_border"
    android:padding="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:background="@null">

        <!-- 左侧导航栏 (添加了分隔竖线背景) -->
        <LinearLayout
            android:layout_width="160dp"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:background="@drawable/nav_separator"> <!-- 新添加的背景 -->
            <!-- 网络 -->
            <LinearLayout
                android:id="@+id/network_tab_container"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:background="@drawable/selector_settings_tab"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:id="@+id/network_icon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_settings"
                    android:scaleType="centerInside"
                    android:adjustViewBounds="true"/>

                <TextView
                    android:id="@+id/item_network"
                    android:layout_width="96dp"
                    android:layout_height="wrap_content"
                    android:background="@drawable/selector_tab_background"
                    android:paddingStart="8dp"
                    android:text="网络"
                    android:textColor="#000000"
                    android:textSize="20sp" />
            </LinearLayout>
            <!-- 存储设置 -->
            <LinearLayout
                android:id="@+id/storage_tab_container"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:background="@drawable/selector_settings_tab"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:id="@+id/storage_icon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_settings"
                    android:scaleType="centerInside"
                    android:adjustViewBounds="true"/>

                <TextView
                    android:id="@+id/item_storage"
                    android:layout_width="96dp"
                    android:layout_height="wrap_content"
                    android:background="@drawable/selector_tab_background"
                    android:paddingStart="8dp"
                    android:text="存储设置"
                    android:textColor="#000000"
                    android:textSize="20sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/format_tab_container"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:background="@drawable/selector_settings_tab"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:id="@+id/format_icon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_settings"
                    android:scaleType="centerInside"
                    android:adjustViewBounds="true"/>

                <TextView
                    android:id="@+id/item_format"
                    android:layout_width="96dp"
                    android:layout_height="wrap_content"
                    android:background="@drawable/selector_tab_background"
                    android:paddingStart="8dp"
                    android:paddingTop="0dp"
                    android:paddingBottom="0dp"
                    android:text="图像设置"
                    android:textColor="#000000"
                    android:textSize="20sp" />
            </LinearLayout>

            <!-- 录像选项 -->
            <LinearLayout
                android:id="@+id/video_tab_container"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:background="@drawable/selector_settings_tab"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:id="@+id/video_icon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:adjustViewBounds="true"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_settings"/>

                <TextView
                    android:id="@+id/item_video"
                    android:layout_width="96dp"
                    android:layout_height="wrap_content"
                    android:background="@drawable/selector_tab_background"
                    android:paddingStart="8dp"
                    android:text="录像设置"
                    android:textColor="#000000"
                    android:textSize="20sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/measurement_tab_container"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:background="@drawable/selector_settings_tab"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:id="@+id/measurement_icon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_settings"
                    android:scaleType="centerInside"
                    android:adjustViewBounds="true"/>

                <TextView
                    android:id="@+id/item_measurement"
                    android:layout_width="96dp"
                    android:layout_height="wrap_content"
                    android:background="@drawable/selector_tab_background"
                    android:paddingStart="8dp"
                    android:text="测量"
                    android:textColor="#000000"
                    android:textSize="20sp" />
            </LinearLayout>

            <!-- 杂项选项 -->
            <LinearLayout
                android:id="@+id/misc_tab_container"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:background="@drawable/selector_settings_tab"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:id="@+id/about_icon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_settings"
                    android:scaleType="centerInside"
                    android:adjustViewBounds="true"/>

                <TextView
                    android:id="@+id/item_misc"
                    android:layout_width="96dp"
                    android:layout_height="wrap_content"
                    android:paddingStart="8dp"
                    android:text="杂项"
                    android:textSize="20sp" />
            </LinearLayout>
        </LinearLayout>

        <!-- 右侧内容容器 -->
        <FrameLayout
            android:id="@+id/content_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/nav_separator"/>
    </LinearLayout>

    <ImageButton
        android:id="@+id/btn_close"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_gravity="top|end"
        android:layout_margin="12dp"
        android:src="@drawable/ic_close"
        android:background="@color/gray_text_disabled"/>
</FrameLayout>