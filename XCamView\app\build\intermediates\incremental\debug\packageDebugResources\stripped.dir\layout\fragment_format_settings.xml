<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:divider="@drawable/divider"
        android:showDividers="middle">


        <!-- 图像格式设置 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:background="@drawable/border_box"
            android:layout_marginTop="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="图像格式"
                android:textSize="18sp"
                android:textStyle="bold"/>

            <RadioGroup
                android:id="@+id/format_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp">

                <RadioButton
                    android:id="@+id/radio_jpeg"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="JPEG"/>

                <RadioButton
                    android:id="@+id/radio_bmp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="BMP"/>

                <RadioButton
                    android:id="@+id/radio_png"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="PNG"/>

                <RadioButton
                    android:id="@+id/radio_tiff"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="TIFF"/>
            </RadioGroup>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:background="@drawable/border_box"
            android:layout_marginTop="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="测量设置"
                android:textSize="18sp"
                android:textStyle="bold"/>

            <!-- 保存方式 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="保存方式"
                android:textSize="16sp"
                android:layout_marginTop="16dp"/>

            <RadioGroup
                android:id="@+id/save_mode_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp">

                <RadioButton
                    android:id="@+id/radio_fusion_mode"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="融合模式"/>

                <RadioButton
                    android:id="@+id/radio_layer_mode"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="分层模式"/>
            </RadioGroup>

            <!-- 测量数据 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="测量数据"
                android:textSize="16sp"
                android:layout_marginTop="16dp"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="8dp">

                <CheckBox
                    android:id="@+id/check_save_measurement_marks"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="保存测量标注"/>

                <CheckBox
                    android:id="@+id/check_save_measurement_values"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="保存测量数值"/>

                <CheckBox
                    android:id="@+id/check_save_measurement_report"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="保存测量报告"/>
            </LinearLayout>

            <!-- 标注样式 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="标注样式"
                android:textSize="16sp"
                android:layout_marginTop="16dp"/>

            <Spinner
                android:id="@+id/spinner_label_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginRight="600dp"
                android:entries="@array/label_styles"/>

            <!-- 字体大小 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="字体大小"
                android:textSize="16sp"
                android:layout_marginTop="16dp"/>

            <Spinner
                android:id="@+id/spinner_font_size"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginRight="600dp"
                android:entries="@array/font_sizes"/>

            <!-- 按钮区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_marginTop="24dp"
                android:paddingHorizontal="0dp">

                <Button
                    android:id="@+id/btn_style_preview"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="样式预览"
                    android:layout_marginHorizontal="4dp"/>

                <Button
                    android:id="@+id/btn_reset_settings"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="重置设置"
                    android:layout_marginHorizontal="4dp"/>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            android:background="@drawable/border_box"
            android:layout_marginTop="16dp"
            android:visibility="gone">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="高级图像参数"
                android:textSize="18sp"
                android:textStyle="bold"/>

            <!-- 色彩空间 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="色彩空间"
                android:textSize="16sp"
                android:layout_marginTop="16dp"/>
            <RadioGroup
                android:id="@+id/color_space_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <RadioButton
                    android:id="@+id/radio_srgb"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="sRGB"/>
                <RadioButton
                    android:id="@+id/radio_adobe_rgb"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Adobe RGB"/>
            </RadioGroup>

            <!-- 色彩深度 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="色彩深度"
                android:textSize="16sp"
                android:layout_marginTop="16dp"/>
            <RadioGroup
                android:id="@+id/color_depth_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <RadioButton
                    android:id="@+id/radio_8bit"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="8位"/>
                <RadioButton
                    android:id="@+id/radio_16bit"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="16位"/>
            </RadioGroup>

            <!-- 压缩质量 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="压缩质量"
                android:textSize="16sp"
                android:layout_marginTop="16dp"/>
            <Spinner
                android:id="@+id/spinner_compression"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:entries="@array/compression_levels"
                android:paddingLeft="15dp"
                android:paddingStart="15dp"
                android:layout_marginRight="600dp"/>

            <!-- 后期功能 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="后期功能"
                android:textSize="16sp"
                android:layout_marginTop="16dp"/>
            <CheckBox
                android:id="@+id/check_fusion_mode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="启用融合模式"/>
            <CheckBox
                android:id="@+id/check_layer_mode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="启用分层模式"/>

            <!-- 按钮区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_marginTop="24dp">
                <Button
                    android:id="@+id/btn_param_details"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="参数详情"
                    android:layout_marginHorizontal="4dp"/>
                <Button
                    android:id="@+id/btn_compatibility_test"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="兼容性测试"
                    android:layout_marginHorizontal="4dp"/>
            </LinearLayout>
        </LinearLayout>

    </LinearLayout>
</ScrollView>