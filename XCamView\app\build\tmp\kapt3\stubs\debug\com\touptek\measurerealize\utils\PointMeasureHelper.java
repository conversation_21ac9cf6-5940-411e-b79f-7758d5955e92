package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 🎯 专业级点测量助手主类 - 与XCamView架构完全一致
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000p\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0017\u0018\u0000 =2\u00020\u0001:\u0001=B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0017\u001a\u00020\u0018J\u0006\u0010\u0019\u001a\u00020\u0011J\u0006\u0010\u001a\u001a\u00020\u0011J\u0006\u0010\u001b\u001a\u00020\bJ\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u001e0\u001dJ\u0006\u0010\u001f\u001a\u00020 J\b\u0010!\u001a\u0004\u0018\u00010\u001eJ\u0010\u0010\"\u001a\u00020\b2\u0006\u0010#\u001a\u00020$H\u0002J\u001e\u0010%\u001a\u00020\b2\u0006\u0010&\u001a\u00020\'2\u0006\u0010(\u001a\u00020 2\u0006\u0010)\u001a\u00020 J\u0006\u0010*\u001a\u00020\bJ\u0016\u0010+\u001a\u00020\u00112\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010,\u001a\u00020\u0015J\u0006\u0010\t\u001a\u00020\bJ \u0010-\u001a\u00020\b2\u0006\u0010#\u001a\u00020$2\u0006\u0010(\u001a\u00020 2\u0006\u0010)\u001a\u00020 H\u0002J\u000e\u0010.\u001a\u00020\b2\u0006\u0010#\u001a\u00020$J\u000e\u0010/\u001a\u00020\b2\u0006\u0010#\u001a\u00020$J\u000e\u00100\u001a\u00020\b2\u0006\u0010#\u001a\u00020$J\b\u00101\u001a\u00020\u0011H\u0002J\u0006\u00102\u001a\u00020\u0011J\u0006\u00103\u001a\u00020\u0011J\u0006\u00104\u001a\u00020\u0011J\b\u00105\u001a\u00020\u0011H\u0002J\u0006\u00106\u001a\u00020\u0011J\u0010\u00107\u001a\u00020\u00112\u0006\u00108\u001a\u00020\u0004H\u0002J\u0014\u00109\u001a\u00020\u00112\f\u0010:\u001a\b\u0012\u0004\u0012\u00020\u00110\u0010J\u0010\u0010;\u001a\u00020\u00112\u0006\u00108\u001a\u00020\u0004H\u0002J\u0006\u0010<\u001a\u00020\u0018R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000f\u001a\n\u0012\u0004\u0012\u00020\u0011\u0018\u00010\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00040\u0013X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0016\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006>"}, d2 = {"Lcom/touptek/measurerealize/utils/PointMeasureHelper;", "", "()V", "activeMeasurement", "Lcom/touptek/measurerealize/utils/PointMeasurement;", "imageView", "Landroid/widget/ImageView;", "isCreatingNew", "", "isDraggingPoint", "lastTouchX", "", "lastTouchY", "longPressStartTime", "", "measurementUpdateCallback", "Lkotlin/Function0;", "", "measurements", "Ljava/util/ArrayList;", "originalBitmap", "Landroid/graphics/Bitmap;", "selectedMeasurement", "addNewMeasurement", "", "clearAllMeasurements", "clearSelection", "deleteSelectedMeasurement", "getAllMeasurementData", "", "Lcom/touptek/measurerealize/utils/PointMeasurementData;", "getMeasurementCount", "", "getMeasurementData", "handleLongPress", "touchPoint", "Landroid/graphics/PointF;", "handleTouchEvent", "event", "Landroid/view/MotionEvent;", "viewWidth", "viewHeight", "hasSelectedMeasurement", "init", "bitmap", "isInImageContentArea", "isNearAnyMeasurement", "isPointOnMeasurement", "isTouchingMeasurementPoint", "notifyUpdate", "onScaleChanged", "pauseMeasurement", "reset", "resetInteractionState", "resumeMeasurement", "selectMeasurement", "measurement", "setMeasurementUpdateCallback", "callback", "startDraggingPoint", "startNewMeasurement", "Companion", "app_debug"})
public final class PointMeasureHelper {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.measurerealize.utils.PointMeasureHelper.Companion Companion = null;
    private static final java.lang.String TAG = "PointMeasureHelper";
    private static final float TOUCH_RADIUS = 80.0F;
    private static final float CLICK_TOLERANCE = 20.0F;
    private static final long LONG_PRESS_DURATION = 800L;
    private android.widget.ImageView imageView;
    private android.graphics.Bitmap originalBitmap;
    private final java.util.ArrayList<com.touptek.measurerealize.utils.PointMeasurement> measurements = null;
    private com.touptek.measurerealize.utils.PointMeasurement activeMeasurement;
    private com.touptek.measurerealize.utils.PointMeasurement selectedMeasurement;
    private boolean isDraggingPoint = false;
    private boolean isCreatingNew = false;
    private float lastTouchX = 0.0F;
    private float lastTouchY = 0.0F;
    private long longPressStartTime = 0L;
    private kotlin.jvm.functions.Function0<kotlin.Unit> measurementUpdateCallback;
    
    public PointMeasureHelper() {
        super();
    }
    
    /**
     * 🚀 初始化助手 - 与AngleMeasureHelper保持一致
     */
    public final void init(@org.jetbrains.annotations.NotNull
    android.widget.ImageView imageView, @org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
    }
    
    private final void resetInteractionState() {
    }
    
    /**
     * 🎯 开始新的点测量 - 在屏幕中心生成默认点
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String startNewMeasurement() {
        return null;
    }
    
    /**
     * 🔔 通知更新
     */
    private final void notifyUpdate() {
    }
    
    /**
     * 🎯 设置测量更新回调
     */
    public final void setMeasurementUpdateCallback(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    /**
     * 📊 获取所有测量数据用于覆盖层显示
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.touptek.measurerealize.utils.PointMeasurementData> getAllMeasurementData() {
        return null;
    }
    
    /**
     * 🎨 获取当前选中的测量数据（向后兼容）
     */
    @org.jetbrains.annotations.Nullable
    public final com.touptek.measurerealize.utils.PointMeasurementData getMeasurementData() {
        return null;
    }
    
    /**
     * 🎯 处理触摸事件 - 与AngleMeasureHelper保持一致的交互逻辑
     */
    public final boolean handleTouchEvent(@org.jetbrains.annotations.NotNull
    android.view.MotionEvent event, int viewWidth, int viewHeight) {
        return false;
    }
    
    private final void selectMeasurement(com.touptek.measurerealize.utils.PointMeasurement measurement) {
    }
    
    private final void startDraggingPoint(com.touptek.measurerealize.utils.PointMeasurement measurement) {
    }
    
    private final boolean handleLongPress(android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🛡️ 检测触摸点是否在图像内容区域（避免UI按钮区域）
     */
    private final boolean isInImageContentArea(android.graphics.PointF touchPoint, int viewWidth, int viewHeight) {
        return false;
    }
    
    /**
     * 🎯 获取测量数量
     */
    public final int getMeasurementCount() {
        return 0;
    }
    
    /**
     * 🎯 检查是否正在拖拽点
     */
    public final boolean isDraggingPoint() {
        return false;
    }
    
    /**
     * 🎯 检查是否触摸到测量点
     */
    public final boolean isTouchingMeasurementPoint(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🎯 添加新的点测量 - 与AngleMeasureHelper保持一致
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String addNewMeasurement() {
        return null;
    }
    
    /**
     * 🗑️ 删除选中的测量
     */
    public final boolean deleteSelectedMeasurement() {
        return false;
    }
    
    /**
     * ⏸️ 暂停测量
     */
    public final void pauseMeasurement() {
    }
    
    /**
     * ▶️ 恢复测量
     */
    public final void resumeMeasurement() {
    }
    
    /**
     * 🧹 清除所有测量
     */
    public final void clearAllMeasurements() {
    }
    
    /**
     * 🧹 重置助手
     */
    public final void reset() {
    }
    
    /**
     * 🎯 检查触摸点是否在任何测量上（用于智能模式激活）
     */
    public final boolean isPointOnMeasurement(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🔍 检查是否有选中的测量
     */
    public final boolean hasSelectedMeasurement() {
        return false;
    }
    
    /**
     * 🔍 检查触摸点是否靠近任何测量
     */
    public final boolean isNearAnyMeasurement(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🔄 清除所有选中状态（用于模式切换时避免冲突）
     */
    public final void clearSelection() {
    }
    
    /**
     * 🔄 处理缩放变化 - 同步所有测量的坐标
     */
    public final void onScaleChanged() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/touptek/measurerealize/utils/PointMeasureHelper$Companion;", "", "()V", "CLICK_TOLERANCE", "", "LONG_PRESS_DURATION", "", "TAG", "", "TOUCH_RADIUS", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}