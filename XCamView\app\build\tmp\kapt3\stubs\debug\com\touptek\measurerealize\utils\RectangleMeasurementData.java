package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 📦 矩形测量数据 - 四角点矩形测量
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u001c\n\u0002\u0010\u0000\n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001B{\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\t\u0012\b\b\u0002\u0010\u000b\u001a\u00020\t\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0006\u0012\b\b\u0002\u0010\r\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u0010\u001a\u00020\t\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\u0002\u0010\u0013J\t\u0010!\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\"\u001a\u00020\tH\u00c6\u0003J\t\u0010#\u001a\u00020\u0012H\u00c6\u0003J\u000f\u0010$\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\u000f\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\t\u0010&\u001a\u00020\tH\u00c6\u0003J\t\u0010\'\u001a\u00020\tH\u00c6\u0003J\t\u0010(\u001a\u00020\tH\u00c6\u0003J\u000b\u0010)\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\t\u0010*\u001a\u00020\u000eH\u00c6\u0003J\t\u0010+\u001a\u00020\u000eH\u00c6\u0003J\u0085\u0001\u0010,\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\t2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u00062\b\b\u0002\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u000e2\b\b\u0002\u0010\u0010\u001a\u00020\t2\b\b\u0002\u0010\u0011\u001a\u00020\u0012H\u00c6\u0001J\u0013\u0010-\u001a\u00020\t2\b\u0010.\u001a\u0004\u0018\u00010/H\u00d6\u0003J\t\u00100\u001a\u00020\u0012H\u00d6\u0001J\t\u00101\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\r\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0017\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0011\u0010\u000b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\u001cR\u0011\u0010\u0010\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u001cR\u0011\u0010\n\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u001cR\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u001cR\u0011\u0010\u000f\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0015R\u0013\u0010\f\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001fR\u0017\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0017\u00a8\u00062"}, d2 = {"Lcom/touptek/measurerealize/utils/RectangleMeasurementData;", "Lcom/touptek/measurerealize/utils/MeasurementData;", "id", "", "viewPoints", "", "Landroid/graphics/PointF;", "bitmapPoints", "isSelected", "", "isEditing", "isCompleted", "textPosition", "area", "", "perimeter", "isDragging", "draggedPointIndex", "", "(Ljava/lang/String;Ljava/util/List;Ljava/util/List;ZZZLandroid/graphics/PointF;DDZI)V", "getArea", "()D", "getBitmapPoints", "()Ljava/util/List;", "getDraggedPointIndex", "()I", "getId", "()Ljava/lang/String;", "()Z", "getPerimeter", "getTextPosition", "()Landroid/graphics/PointF;", "getViewPoints", "component1", "component10", "component11", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "", "hashCode", "toString", "app_debug"})
public final class RectangleMeasurementData extends com.touptek.measurerealize.utils.MeasurementData {
    @org.jetbrains.annotations.NotNull
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<android.graphics.PointF> viewPoints = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<android.graphics.PointF> bitmapPoints = null;
    private final boolean isSelected = false;
    private final boolean isEditing = false;
    private final boolean isCompleted = false;
    @org.jetbrains.annotations.Nullable
    private final android.graphics.PointF textPosition = null;
    private final double area = 0.0;
    private final double perimeter = 0.0;
    private final boolean isDragging = false;
    private final int draggedPointIndex = 0;
    
    /**
     * 📦 矩形测量数据 - 四角点矩形测量
     */
    @org.jetbrains.annotations.NotNull
    public final com.touptek.measurerealize.utils.RectangleMeasurementData copy(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.util.List<? extends android.graphics.PointF> viewPoints, @org.jetbrains.annotations.NotNull
    java.util.List<? extends android.graphics.PointF> bitmapPoints, boolean isSelected, boolean isEditing, boolean isCompleted, @org.jetbrains.annotations.Nullable
    android.graphics.PointF textPosition, double area, double perimeter, boolean isDragging, int draggedPointIndex) {
        return null;
    }
    
    /**
     * 📦 矩形测量数据 - 四角点矩形测量
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 📦 矩形测量数据 - 四角点矩形测量
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 📦 矩形测量数据 - 四角点矩形测量
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public RectangleMeasurementData(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.util.List<? extends android.graphics.PointF> viewPoints, @org.jetbrains.annotations.NotNull
    java.util.List<? extends android.graphics.PointF> bitmapPoints, boolean isSelected, boolean isEditing, boolean isCompleted, @org.jetbrains.annotations.Nullable
    android.graphics.PointF textPosition, double area, double perimeter, boolean isDragging, int draggedPointIndex) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> getViewPoints() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> getBitmapPoints() {
        return null;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean isSelected() {
        return false;
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final boolean isEditing() {
        return false;
    }
    
    public final boolean component6() {
        return false;
    }
    
    public final boolean isCompleted() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final android.graphics.PointF component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final android.graphics.PointF getTextPosition() {
        return null;
    }
    
    public final double component8() {
        return 0.0;
    }
    
    public final double getArea() {
        return 0.0;
    }
    
    public final double component9() {
        return 0.0;
    }
    
    public final double getPerimeter() {
        return 0.0;
    }
    
    public final boolean component10() {
        return false;
    }
    
    public final boolean isDragging() {
        return false;
    }
    
    public final int component11() {
        return 0;
    }
    
    public final int getDraggedPointIndex() {
        return 0;
    }
}