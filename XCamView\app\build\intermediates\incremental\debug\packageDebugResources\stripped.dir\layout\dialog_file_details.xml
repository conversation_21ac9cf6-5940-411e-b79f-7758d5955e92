<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="100dp"
    android:layout_height="wrap_content"
    android:minHeight="300dp"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@android:color/white">

    <!-- 通用文件信息 -->
    <TextView
        android:id="@+id/tv_file_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="16sp"
        android:textColor="@android:color/black"
        android:paddingBottom="12dp"/>

    <TextView
        android:id="@+id/tv_file_size"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="16sp"
        android:textColor="@android:color/black"
        android:paddingBottom="12dp"/>

    <TextView
        android:id="@+id/tv_modified_date"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="16sp"
        android:textColor="@android:color/black"
        android:paddingBottom="16dp"/>

    <!-- 视频专属信息 -->
    <LinearLayout
        android:id="@+id/video_info_group"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:id="@+id/tv_video_duration"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="@android:color/black"
            android:paddingBottom="12dp"/>

        <TextView
            android:id="@+id/tv_video_bitrate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="@android:color/black"
            android:paddingBottom="12dp"/>

        <TextView
            android:id="@+id/tv_video_resolution"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="@android:color/black"
            android:paddingBottom="12dp"/>

        <TextView
            android:id="@+id/tv_video_framerate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="@android:color/black"/>
    </LinearLayout>
</LinearLayout>