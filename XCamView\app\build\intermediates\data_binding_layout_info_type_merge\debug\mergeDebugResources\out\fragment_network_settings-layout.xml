<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_network_settings" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\fragment_network_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_network_settings_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="406" endOffset="12"/></Target><Target id="@+id/tvEthernetStatus" view="TextView"><Expressions/><location startLine="47" startOffset="16" endLine="53" endOffset="51"/></Target><Target id="@+id/tvEthernetIp" view="TextView"><Expressions/><location startLine="70" startOffset="16" endLine="75" endOffset="44"/></Target><Target id="@+id/btnEthernetConfig" view="Button"><Expressions/><location startLine="84" startOffset="16" endLine="90" endOffset="51"/></Target><Target id="@+id/btnDisconnectEthernet" view="Button"><Expressions/><location startLine="92" startOffset="16" endLine="99" endOffset="53"/></Target><Target id="@+id/tvStaLinkStatusTitle" view="TextView"><Expressions/><location startLine="136" startOffset="16" endLine="142" endOffset="51"/></Target><Target id="@+id/tvSTAStatus" view="TextView"><Expressions/><location startLine="144" startOffset="16" endLine="150" endOffset="51"/></Target><Target id="@+id/staCurrentNetTitle" view="TextView"><Expressions/><location startLine="162" startOffset="16" endLine="168" endOffset="51"/></Target><Target id="@+id/tvCurrentWifi" view="TextView"><Expressions/><location startLine="170" startOffset="16" endLine="176" endOffset="52"/></Target><Target id="@+id/btnToggleWifi" view="Button"><Expressions/><location startLine="181" startOffset="12" endLine="188" endOffset="50"/></Target><Target id="@+id/ApStatusTitle" view="TextView"><Expressions/><location startLine="211" startOffset="20" endLine="217" endOffset="55"/></Target><Target id="@+id/tvApStatus" view="TextView"><Expressions/><location startLine="219" startOffset="20" endLine="225" endOffset="48"/></Target><Target id="@+id/tvApSsidTitle" view="TextView"><Expressions/><location startLine="227" startOffset="20" endLine="234" endOffset="55"/></Target><Target id="@+id/tvApSsid" view="TextView"><Expressions/><location startLine="236" startOffset="20" endLine="241" endOffset="48"/></Target><Target id="@+id/btnToggleAp" view="Button"><Expressions/><location startLine="244" startOffset="16" endLine="250" endOffset="45"/></Target><Target id="@+id/tvRtspStatus" view="TextView"><Expressions/><location startLine="283" startOffset="16" endLine="289" endOffset="51"/></Target><Target id="@+id/tvRtspUri" view="TextView"><Expressions/><location startLine="306" startOffset="16" endLine="313" endOffset="46"/></Target><Target id="@+id/spinnerNetworkInterface" view="Spinner"><Expressions/><location startLine="330" startOffset="16" endLine="334" endOffset="21"/></Target><Target id="@+id/rgStreamType" view="RadioGroup"><Expressions/><location startLine="351" startOffset="16" endLine="375" endOffset="28"/></Target><Target id="@+id/rbCameraStream" view="RadioButton"><Expressions/><location startLine="359" startOffset="20" endLine="366" endOffset="56"/></Target><Target id="@+id/rbScreenStream" view="RadioButton"><Expressions/><location startLine="368" startOffset="20" endLine="374" endOffset="56"/></Target><Target id="@+id/btnStartStream" view="Button"><Expressions/><location startLine="385" startOffset="16" endLine="391" endOffset="51"/></Target><Target id="@+id/btnStopStream" view="Button"><Expressions/><location startLine="393" startOffset="16" endLine="400" endOffset="58"/></Target></Targets></Layout>