package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 🔵 椭圆测量助手 - 专业级椭圆测量实现
 *
 * 核心特性：
 * - 长轴端点自由移动，支持椭圆任意角度旋转
 * - 短轴端点垂直约束，确保几何正确性
 * - 实时计算面积和周长
 * - 智能触摸检测和拖拽控制
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000j\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\n\u0018\u0000 32\u00020\u0001:\u00013B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0018\u001a\u00020\u0011J\u0006\u0010\u0019\u001a\u00020\u0011J\u0006\u0010\u001a\u001a\u00020\bJ\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00140\u001cJ\u0006\u0010\u001d\u001a\u00020\u0004J\u0006\u0010\u001e\u001a\u00020\u001fJ\u001e\u0010 \u001a\u00020\b2\u0006\u0010!\u001a\u00020\"2\u0006\u0010#\u001a\u00020\u00042\u0006\u0010$\u001a\u00020\u0004J\u0006\u0010%\u001a\u00020\bJ\u0016\u0010&\u001a\u00020\u00112\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\'\u001a\u00020\u0016J\u0006\u0010\u0007\u001a\u00020\bJ \u0010(\u001a\u00020\b2\u0006\u0010)\u001a\u00020*2\u0006\u0010#\u001a\u00020\u00042\u0006\u0010$\u001a\u00020\u0004H\u0002J\u000e\u0010+\u001a\u00020\b2\u0006\u0010)\u001a\u00020*J\u0006\u0010,\u001a\u00020\u0011J\u0006\u0010-\u001a\u00020\u0011J\b\u0010.\u001a\u00020\u0011H\u0002J\u0014\u0010/\u001a\u00020\u00112\f\u00100\u001a\b\u0012\u0004\u0012\u00020\u00110\u0010J\u0006\u00101\u001a\u00020\u001fJ\u0006\u00102\u001a\u00020\u0011R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000f\u001a\n\u0012\u0004\u0012\u00020\u0011\u0018\u00010\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00140\u0013X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0017\u001a\u0004\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00064"}, d2 = {"Lcom/touptek/measurerealize/utils/EllipseMeasureHelper;", "", "()V", "draggedPointIndex", "", "imageView", "Lcom/touptek/measurerealize/TpImageView;", "isDraggingPoint", "", "isInitialized", "lastTouchX", "", "lastTouchY", "longPressStartTime", "", "measurementUpdateCallback", "Lkotlin/Function0;", "", "measurements", "Ljava/util/ArrayList;", "Lcom/touptek/measurerealize/utils/EllipseMeasurement;", "originalBitmap", "Landroid/graphics/Bitmap;", "selectedMeasurement", "clearAllMeasurements", "clearSelection", "deleteSelectedMeasurement", "getAllMeasurements", "", "getMeasurementCount", "getMeasurementStats", "", "handleTouchEvent", "event", "Landroid/view/MotionEvent;", "viewWidth", "viewHeight", "hasSelectedMeasurement", "init", "bitmap", "isInImageContentArea", "touchPoint", "Landroid/graphics/PointF;", "isNearAnyMeasurement", "onScaleChanged", "recoverFromInvalidState", "resetInteractionState", "setMeasurementUpdateCallback", "callback", "startNewMeasurement", "syncAllCoordinates", "Companion", "app_debug"})
public final class EllipseMeasureHelper {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.measurerealize.utils.EllipseMeasureHelper.Companion Companion = null;
    private static final java.lang.String TAG = "EllipseMeasureHelper";
    private static final float TOUCH_RADIUS = 80.0F;
    private static final float CLICK_TOLERANCE = 20.0F;
    private static final long LONG_PRESS_DURATION = 800L;
    private com.touptek.measurerealize.TpImageView imageView;
    private android.graphics.Bitmap originalBitmap;
    private boolean isInitialized = false;
    private final java.util.ArrayList<com.touptek.measurerealize.utils.EllipseMeasurement> measurements = null;
    private com.touptek.measurerealize.utils.EllipseMeasurement selectedMeasurement;
    private boolean isDraggingPoint = false;
    private int draggedPointIndex = -1;
    private long longPressStartTime = 0L;
    private float lastTouchX = 0.0F;
    private float lastTouchY = 0.0F;
    private kotlin.jvm.functions.Function0<kotlin.Unit> measurementUpdateCallback;
    
    public EllipseMeasureHelper() {
        super();
    }
    
    /**
     * 🔵 初始化椭圆测量助手
     */
    public final void init(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.TpImageView imageView, @org.jetbrains.annotations.NotNull
    android.graphics.Bitmap bitmap) {
    }
    
    /**
     * 🔵 设置测量更新回调
     */
    public final void setMeasurementUpdateCallback(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> callback) {
    }
    
    /**
     * 🔵 创建新椭圆测量
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String startNewMeasurement() {
        return null;
    }
    
    /**
     * 🔵 重置交互状态
     */
    private final void resetInteractionState() {
    }
    
    /**
     * 🔵 获取测量数量
     */
    public final int getMeasurementCount() {
        return 0;
    }
    
    /**
     * 🔵 检查是否有选中的测量
     */
    public final boolean hasSelectedMeasurement() {
        return false;
    }
    
    /**
     * 🔵 检查是否靠近任何测量
     */
    public final boolean isNearAnyMeasurement(@org.jetbrains.annotations.NotNull
    android.graphics.PointF touchPoint) {
        return false;
    }
    
    /**
     * 🔵 获取所有测量数据
     */
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.touptek.measurerealize.utils.EllipseMeasurement> getAllMeasurements() {
        return null;
    }
    
    /**
     * 🔵 清除所有选中状态
     */
    public final void clearSelection() {
    }
    
    /**
     * 🔵 处理触摸事件 - 核心交互逻辑
     */
    public final boolean handleTouchEvent(@org.jetbrains.annotations.NotNull
    android.view.MotionEvent event, int viewWidth, int viewHeight) {
        return false;
    }
    
    private final boolean isInImageContentArea(android.graphics.PointF touchPoint, int viewWidth, int viewHeight) {
        return false;
    }
    
    /**
     * 🔵 删除选中的测量 - 智能删除逻辑
     */
    public final boolean deleteSelectedMeasurement() {
        return false;
    }
    
    /**
     * 🔵 清除所有测量
     */
    public final void clearAllMeasurements() {
    }
    
    /**
     * 🔵 状态恢复机制
     */
    public final void recoverFromInvalidState() {
    }
    
    /**
     * 🔵 同步所有测量的坐标（缩放时调用）
     */
    public final void syncAllCoordinates() {
    }
    
    /**
     * 🔵 获取测量统计信息
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getMeasurementStats() {
        return null;
    }
    
    /**
     * 🎯 检查是否正在拖拽点
     */
    public final boolean isDraggingPoint() {
        return false;
    }
    
    /**
     * 🔄 缩放变化时同步坐标
     */
    public final void onScaleChanged() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/touptek/measurerealize/utils/EllipseMeasureHelper$Companion;", "", "()V", "CLICK_TOLERANCE", "", "LONG_PRESS_DURATION", "", "TAG", "", "TOUCH_RADIUS", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}